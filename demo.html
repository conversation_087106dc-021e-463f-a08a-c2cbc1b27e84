<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Level Up Your Life - RPG Goals App</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        body {
            background: #0a0a20;
            color: #00ff9d;
            font-family: 'Inter', sans-serif;
        }
        .shadow-cyberpunk {
            box-shadow: 0 0 15px rgba(0, 255, 157, 0.5), 0 0 30px rgba(0, 255, 255, 0.2);
        }
        .text-neon-cyan { color: #00ffff; }
        .text-neon-green { color: #00ff9d; }
        .bg-card { background-color: #1a1a35; }
        .border-neon-green { border-color: #00ff9d; }
        .shadow-text { text-shadow: 0 0 5px #00ffff, 0 0 10px #00ffff, 0 0 15px #00ffff; }
        .hover-glow:hover { box-shadow: 0 0 20px rgba(0, 255, 157, 0.6), 0 0 40px rgba(0, 255, 255, 0.3); }
    </style>
</head>
<body class="min-h-screen">
    <div class="container mx-auto p-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-6xl font-bold shadow-text mb-4">
                <i class="fas fa-gamepad text-neon-cyan mr-4"></i>
                Level Up Your Life
            </h1>
            <p class="text-2xl text-neon-cyan">RPG Goals App - Demo Page</p>
        </div>

        <!-- Status -->
        <div class="bg-card rounded-lg p-6 border border-neon-green/30 shadow-cyberpunk mb-8">
            <h2 class="text-3xl font-bold text-neon-green mb-4">
                <i class="fas fa-check-circle mr-2"></i>
                Application Status: READY!
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="text-xl font-bold text-neon-cyan mb-2">✅ Features Implemented:</h3>
                    <ul class="space-y-1 text-neon-green">
                        <li><i class="fas fa-star mr-2"></i>Gamified Goal Tracking</li>
                        <li><i class="fas fa-heart mr-2"></i>Daily HP/MP Stats</li>
                        <li><i class="fas fa-map mr-2"></i>Interactive Life Map</li>
                        <li><i class="fas fa-robot mr-2"></i>AI Assistant (Gemini)</li>
                        <li><i class="fas fa-coins mr-2"></i>Lumina Currency System</li>
                        <li><i class="fas fa-trophy mr-2"></i>Achievement System</li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-xl font-bold text-neon-cyan mb-2">🔧 Technical Stack:</h3>
                    <ul class="space-y-1 text-neon-green">
                        <li><i class="fab fa-react mr-2"></i>React 18 + TypeScript</li>
                        <li><i class="fas fa-bolt mr-2"></i>Vite Build System</li>
                        <li><i class="fas fa-palette mr-2"></i>Tailwind CSS + Cyberpunk Theme</li>
                        <li><i class="fas fa-database mr-2"></i>PostgreSQL + localStorage</li>
                        <li><i class="fas fa-brain mr-2"></i>Google Gemini AI</li>
                        <li><i class="fas fa-mobile-alt mr-2"></i>Responsive Design</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="bg-card rounded-lg p-6 border border-neon-green/30 shadow-cyberpunk mb-8">
            <h2 class="text-2xl font-bold text-neon-green mb-4">
                <i class="fas fa-rocket mr-2"></i>
                How to Start the Application:
            </h2>
            <div class="space-y-4">
                <div class="bg-[#0a0a20] rounded p-4 border border-neon-green/30">
                    <h3 class="text-lg font-bold text-neon-cyan mb-2">Option 1: Using Node.js</h3>
                    <code class="text-neon-green">cd "/home/<USER>/Documents/root/live stats" && node server.js</code>
                </div>
                <div class="bg-[#0a0a20] rounded p-4 border border-neon-green/30">
                    <h3 class="text-lg font-bold text-neon-cyan mb-2">Option 2: Using npm</h3>
                    <code class="text-neon-green">cd "/home/<USER>/Documents/root/live stats" && npm run dev</code>
                </div>
                <div class="bg-[#0a0a20] rounded p-4 border border-neon-green/30">
                    <h3 class="text-lg font-bold text-neon-cyan mb-2">Option 3: Direct Vite</h3>
                    <code class="text-neon-green">cd "/home/<USER>/Documents/root/live stats" && ./node_modules/.bin/vite</code>
                </div>
            </div>
        </div>

        <!-- Features Preview -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-card rounded-lg p-6 border border-neon-green/30 hover-glow transition-all duration-300">
                <h3 class="text-xl font-bold text-neon-green mb-3">
                    <i class="fas fa-gamepad mr-2"></i>
                    Quest System
                </h3>
                <p class="text-neon-cyan">Create and track goals as RPG quests with progress bars, streaks, and categories.</p>
            </div>
            <div class="bg-card rounded-lg p-6 border border-neon-cyan/30 hover-glow transition-all duration-300">
                <h3 class="text-xl font-bold text-neon-cyan mb-3">
                    <i class="fas fa-heart-pulse mr-2"></i>
                    Daily Stats
                </h3>
                <p class="text-neon-green">Monitor your HP, MP, and status ailments with AI-powered natural language parsing.</p>
            </div>
            <div class="bg-card rounded-lg p-6 border border-yellow-500/30 hover-glow transition-all duration-300">
                <h3 class="text-xl font-bold text-yellow-400 mb-3">
                    <i class="fas fa-robot mr-2"></i>
                    AI Assistant
                </h3>
                <p class="text-neon-cyan">Chat with Gemini AI to create quests, achievements, and analyze your progress.</p>
            </div>
        </div>

        <!-- API Key Status -->
        <div class="bg-card rounded-lg p-6 border border-green-500/30 shadow-cyberpunk">
            <h2 class="text-2xl font-bold text-green-400 mb-4">
                <i class="fas fa-key mr-2"></i>
                Gemini API Key: CONFIGURED ✅
            </h2>
            <p class="text-neon-cyan">Your AI Assistant is ready to help you create quests and achievements!</p>
            <div class="mt-4 p-3 bg-[#0a0a20] rounded border border-green-500/30">
                <code class="text-green-400">AIzaSyBM5XQNXhsv9BZFsNzjyFghn2Qxkg9rQQk</code>
            </div>
        </div>

        <!-- Footer -->
        <div class="text-center mt-8">
            <p class="text-neon-cyan">
                <i class="fas fa-star mr-2"></i>
                Ready to level up your life? Start the server and begin your journey!
                <i class="fas fa-star ml-2"></i>
            </p>
        </div>
    </div>
</body>
</html>
