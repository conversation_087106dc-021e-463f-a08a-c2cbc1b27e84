-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Goals table
CREATE TABLE IF NOT EXISTS goals (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  category VARCHAR(100),
  current_value FLOAT DEFAULT 0,
  target_value FLOAT DEFAULT 0,
  is_debt BOOLEAN DEFAULT FALSE,
  debt_amount FLOAT DEFAULT 0,
  current_streak INTEGER DEFAULT 0,
  longest_streak INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  daily_repeatable BOOLEAN DEFAULT FALSE,
  unit VARCHAR(50)
);

-- Achievements table
CREATE TABLE IF NOT EXISTS achievements (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  goal_id UUID REFERENCES goals(id) ON DELETE CASCADE,
  name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
  description TEXT,
  icon_class VARCHAR(100),
  requirement_value FLOAT,
  unlocked BOOLEAN DEFAULT FALSE,
  unlocked_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_goals_category ON goals(category);
CREATE INDEX IF NOT EXISTS idx_goals_created_at ON goals(created_at);
CREATE INDEX IF NOT EXISTS idx_achievements_goal_id ON achievements(goal_id);
CREATE INDEX IF NOT EXISTS idx_achievements_unlocked ON achievements(unlocked);
