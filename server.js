#!/usr/bin/env node

const { createServer } = require('vite');

async function startServer() {
  try {
    console.log('🚀 Starting Level Up Your Life - RPG Goals App...');
    
    const server = await createServer({
      server: {
        port: 3000,
        host: '0.0.0.0',
        open: true
      }
    });
    
    await server.listen();
    
    console.log('✅ Server running at:');
    console.log('   Local:   http://localhost:3000');
    console.log('   Network: http://0.0.0.0:3000');
    console.log('');
    console.log('🎮 Ready to level up your life!');
    
  } catch (error) {
    console.error('❌ Error starting server:', error);
    process.exit(1);
  }
}

startServer();
