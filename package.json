{"name": "rpg-goals-app", "version": "0.1.0", "private": true, "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "dependencies": {"@google/generative-ai": "^0.14.1", "@vercel/postgres": "^0.8.0", "convex": "^1.24.8", "react": "^18.3.1", "react-dom": "^18.3.1", "react-helmet": "^6.1.0", "react-router-dom": "^6.24.0", "uuid": "^10.0.0"}, "devDependencies": {"@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/react-helmet": "^6.1.11", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^7.13.1", "@typescript-eslint/parser": "^7.13.1", "@vitejs/plugin-react": "^4.3.1", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.7", "typescript": "^5.2.2", "vite": "^5.3.1"}}