# Level Up Your Life - RPG Goals App

A gamified personal growth tracker with a cyberpunk aesthetic, featuring RPG-style stats, goal tracking, and AI assistance.

## Features

- **🎮 Gamified Goal Tracking**: Transform your goals into RPG-style quests with progress bars, streaks, and achievements
- **📊 Daily Stats Monitoring**: Track your HP (Health Points), MP (Mana Points), and status ailments
- **🗺️ Interactive Life Map**: Visual dashboard showing your progress across different life domains
- **💰 Lumina Currency**: Gamified financial tracking with bank statement import
- **🤖 AI Assistant**: Natural language processing for quest creation, achievement suggestions, and stats analysis
- **🏆 Achievement System**: Unlock milestones and celebrate your progress
- **📱 Responsive Design**: Works seamlessly on desktop and mobile devices

## Technology Stack

- **Frontend**: React 18, TypeScript, Tailwind CSS, React Router
- **Backend**: Serverless functions with PostgreSQL
- **AI Integration**: Google Gemini API
- **Build Tool**: Vite
- **Database**: PostgreSQL with UUID support

## 🚀 Quick Start

### ✅ **READY TO RUN!** Your app is already set up with:
- ✅ All dependencies installed
- ✅ Gemini API key configured: `AIzaSyBM5XQNXhsv9BZFsNzjyFghn2Qxkg9rQQk`
- ✅ Complete React + TypeScript application
- ✅ Cyberpunk-themed UI with all features

### **Start the Application:**

**Option 1: Using the custom server script**
```bash
cd "/home/<USER>/Documents/root/live stats"
node server.js
```

**Option 2: Using npm (if available)**
```bash
cd "/home/<USER>/Documents/root/live stats"
npm run dev
```

**Option 3: Direct Vite command**
```bash
cd "/home/<USER>/Documents/root/live stats"
./node_modules/.bin/vite --port 3000
```

### **Access Your App:**
- Open browser to: `http://localhost:3000`
- Or view the demo page: `file:///home/<USER>/Documents/root/live%20stats/demo.html`

## Core Concepts

### Quests (Goals)
- **Progress Quests**: Traditional goals with target values (e.g., "Read 12 books")
- **Daily Repeatable**: Habits you want to build (e.g., "Daily workout")
- **Debt Quests**: Things you want to reduce (e.g., "Pay off credit card")

### Daily Stats
- **HP (Health Points)**: Physical health and energy (0-100)
- **MP (Mana Points)**: Mental energy and focus (0-100)
- **Status Ailments**: Temporary conditions affecting you (Fatigue, Stress, etc.)

### Life Map Zones
- **Knowledge Spire**: Learning & Productivity goals
- **Training Grid**: Fitness & Health goals
- **Lumina Vault**: Financial goals and balance
- **Tranquility Core**: Wellness, Mental & Social goals

### Lumina Currency
- Import CSV bank statements for automatic balance updates
- Visual representation of financial health
- Gamified approach to money management

## AI Assistant Commands

The AI assistant understands natural language and can help with:

### Quest Creation
- "Create a daily workout quest for me"
- "Suggest a learning goal for TypeScript"
- "I want to track my reading habit"

### Achievement Creation
- "Create an achievement for 10 workout sessions"
- "Make a milestone for reading 5 books"
- "Achievement for maintaining 7-day streak"

### Stats Analysis
- "Show my HP trends this week"
- "Analyze my energy patterns"
- "Compare my MP today vs yesterday"

## Project Structure

```
src/
├── backend/           # Serverless functions
│   ├── goals.ts      # Goal management
│   ├── debt.ts       # Debt tracking
│   └── achievements.ts # Achievement system
├── components/        # Reusable UI components
│   ├── Layout.tsx    # Main layout with navigation
│   ├── CyberButton.tsx
│   ├── CyberInput.tsx
│   ├── CyberTextArea.tsx
│   ├── LoadingSpinner.tsx
│   └── HpBar.tsx     # Progress visualization
├── pages/            # Main application pages
│   ├── DashboardPage.tsx      # Interactive life map
│   ├── DailyOpsReportPage.tsx # Daily stats tracking
│   ├── NewQuestPage.tsx       # Goal creation
│   ├── UpdateGoalPage.tsx     # Progress tracking
│   ├── AIAssistantPage.tsx    # AI chat interface
│   └── DocumentationPage.tsx  # User guide
├── services/         # API and data management
│   └── api.ts       # All API calls and localStorage
├── constants.ts     # App constants and routes
├── types.ts        # TypeScript interfaces
└── App.tsx         # Main app component
```

## Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build

### Adding New Features

1. **New Quest Types**: Extend the `Goal` interface in `types.ts`
2. **New AI Commands**: Update the prompt in `services/api.ts`
3. **New Life Map Zones**: Modify the dashboard SVG and zone logic
4. **New Status Ailments**: Add to the `STATUS_AILMENTS` constant

## Deployment

The app is designed to work with Vercel for easy deployment:

1. Connect your repository to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

MIT License - see LICENSE file for details