Master Prompt: Build "Level Up Your Life - RPG Goals App" (Complete Specification)
You are an expert React and TypeScript frontend and backend engineer. Your primary task is to build the complete "Level Up Your Life - RPG Goals App" from scratch, strictly following the detailed specifications below. This app is a gamified personal growth tracker with a cyberpunk, neon aesthetic, featuring core RPG stats, a dynamic life map, and an integrated AI assistant.

**Overall Project Goal:** Develop a responsive web application that allows users to track goals, manage daily stats (HP, MP, Ailments), earn achievements, and interact with an AI assistant for content generation, all within a visually engaging RPG theme.

---

## 1. Project Setup & Core Structure

**Technology Stack:**
* **Frontend:** React (functional components, hooks), TypeScript, Tailwind CSS (via CDN), Font Awesome (via CDN), `react-router-dom`, `react-helmet`.
* **Backend (Serverless Functions):** TypeScript, PostgreSQL database, using `@vercel/postgres` for database interactions.
* **AI Integration:** Google Gemini API (`@google/generative-ai`).

**Project Directory Structure:**
/rpg-goals-app
├── public/
│   └── index.html
├── src/
│   ├── backend/        # For serverless functions (PostgreSQL interaction)
│   ├── components/     # Reusable React UI components
│   │   └── icons/      # SVG icon components
│   ├── pages/          # Main application pages
│   ├── services/       # Frontend API interaction logic (localStorage, fetch to backend/AI)
│   ├── constants.ts    # Route definitions, keys
│   ├── types.ts        # All TypeScript interfaces/types
│   ├── App.tsx         # Main React application and routing
│   └── index.tsx       # ReactDOM root rendering
└── package.json


---

## 2. Database Schema (PostgreSQL)

**Create the following tables:**

```sql
CREATE TABLE goals (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  category VARCHAR(100),
  current_value FLOAT DEFAULT 0,
  target_value FLOAT DEFAULT 0,
  is_debt BOOLEAN DEFAULT FALSE,
  debt_amount FLOAT DEFAULT 0,
  current_streak INTEGER DEFAULT 0,
  longest_streak INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  daily_repeatable BOOLEAN DEFAULT FALSE, -- NEW: For daily quests
  unit VARCHAR(50) -- NEW: For daily quest units (e.g., "miles", "reps")
);

CREATE TABLE achievements (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  goal_id UUID REFERENCES goals(id), -- Nullable for global achievements
  name VARCHAR(255) NOT NULL,
  description TEXT,
  icon_class VARCHAR(100), -- e.g., "fa-solid fa-trophy"
  requirement_value FLOAT,
  unlocked BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
3. Backend Functions (src/backend/)
Implement the following functions as serverless function handlers. They will use sql from @vercel/postgres and handle mapping snake_case to camelCase for frontend.

Goals Management (src/backend/goals.ts):

TypeScript

// src/backend/goals.ts
import { sql } from '@vercel/postgres';
import { Goal } from '../types'; // Adjust path as needed

// Define request body types for clarity
interface CreateGoalRequestBody { /* ... fields matching Goal type's DB representation ... */ }
interface UpdateGoalProgressRequestBody { /* ... fields ... */ }

// getGoalsHandler function
export async function getGoalsHandler(): Promise<{ goals: Goal[] }> {
  // ... SQL SELECT * FROM goals, map to camelCase, order by created_at DESC ...
  // Include daily_repeatable and unit in SELECT and mapping
}

// createGoalHandler function
export async function createGoalHandler(body: CreateGoalRequestBody): Promise<{ goal: Goal }> {
  // ... SQL INSERT INTO goals, RETURNING * ...
  // Include daily_repeatable and unit in INSERT
}

// updateGoalProgressHandler function
export async function updateGoalProgressHandler(body: UpdateGoalProgressRequestBody): Promise<{ goal: Goal }> {
  // ... SQL UPDATE goals SET current_value = ..., RETURNING * ...
  // Include daily_repeatable and unit in RETURNING
}
Debt Management (src/backend/debt.ts):

TypeScript

// src/backend/debt.ts
import { sql } from '@vercel/postgres';
import { Goal } from '../types'; // Adjust path as needed

// Define request body type
interface UpdateDebtRequestBody { goalId: string; debt_amount: number; }

// updateDebtHandler function
export async function updateDebtHandler(body: UpdateDebtRequestBody): Promise<{ goal: Goal }> {
  // ... SQL UPDATE goals SET debt_amount = ... WHERE id = ... AND is_debt = true, RETURNING * ...
}
Achievements Management (src/backend/achievements.ts):

TypeScript

// src/backend/achievements.ts
import { sql } from '@vercel/postgres';
import { Achievement } from '../types'; // Adjust path as needed

// Define request body type
interface GetAchievementsRequestBody { goalId?: string; } // goalId is optional for global achievements

// getAchievementsHandler function
export async function getAchievementsHandler(body?: GetAchievementsRequestBody): Promise<{ achievements: Achievement[] }> {
  // ... SQL SELECT * FROM achievements ...
  // If goalId is provided, add WHERE goal_id = ${goalId}.
  // Order by requirement_value ASC.
}

// (Optional, if needed for direct achievement creation, not just AI template conversion)
// createAchievementHandler function
// export async function createAchievementHandler(body: CreateAchievementRequestBody): Promise<{ achievement: Achievement }> { ... }
4. Frontend Data Models (src/types.ts)
Define all necessary TypeScript interfaces. Ensure uuidv4 is imported for client-side ID generation if applicable.

TypeScript

// src/types.ts
import { v4 as uuidv4 } from 'uuid';

export interface Goal {
  id: string;
  name: string;
  description?: string;
  category: string;
  currentValue: number;
  targetValue: number;
  isDebt: boolean;
  debtAmount?: number;
  currentStreak: number;
  longestStreak: number;
  createdAt: number; // Unix timestamp
  dailyRepeatable?: boolean; // NEW
  unit?: string; // NEW
}

export interface Achievement {
  id: string;
  goalId?: string; // Nullable
  name: string;
  description: string;
  iconClass?: string;
  requirementValue?: number;
  unlocked: boolean;
  unlockedAt?: number;
  createdAt: number; // Unix timestamp
}

export interface DailyLogEntry {
  id: string;
  date: string; // YYYY-MM-DD
  currentHP: number;
  maxHP: number;
  currentMP: number;
  maxMP: number;
  statusAilments: string[]; // List of ailments
  rawUserInput?: string;
  notes?: string;
  timestamp: number; // Unix timestamp
}

export interface DailyLogEntryInput {
  rawInput: string;
  hp?: number;
  mp?: number;
  statusAilments?: string[];
}

// For AI responses
export interface AIResponse {
  type: 'achievement_template' | 'daily_stats_query' | 'quest_suggestion' | 'general_response' | 'daily_stats_summary' | 'daily_stats_trend' | 'daily_stats_comparison' | 'error';
  payload?: any;
  message?: string;
}

// For AI-generated achievement proposals
export interface AchievementTemplate {
  name: string;
  description: string;
  condition?: string;
  category?: string;
  linkedQuestName?: string | null; // Name of quest to link to
}

// For parsing bank transactions
export interface BankTransaction {
  date: string; // YYYY-MM-DD
  description: string;
  amount: number; // Positive for credit, negative for debit
  type: string; // "DEBIT", "CREDIT"
  rawLine?: string; // Original line from file
}
5. Frontend API Service (src/services/api.ts)
Implement all frontend API interaction logic. Where no backend function is specified, use localStorage for data persistence (e.g., Lumina, Daily Logs). All functions should be async and return Promise.

TypeScript

// src/services/api.ts
import { Goal, Achievement, DailyLogEntry, DailyLogEntryInput, AIResponse, AchievementTemplate, BankTransaction } from '../types';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { v4 as uuidv4 } from 'uuid'; // For client-side ID generation

// --- localStorage Keys (for data not directly from PostgreSQL backend for simplicity) ---
const DAILY_LOGS_STORAGE_KEY = 'rpg_daily_logs';
const LUMINA_STORAGE_KEY = 'rpg_lumina_balance';
const ACHIEVEMENTS_STORAGE_KEY = 'rpg_achievements'; // For achievements not yet in DB, or for AI-generated ones.

// --- Gemini API Setup ---
const GEMINI_API_KEY = import.meta.env.VITE_GEMINI_API_KEY || 'YOUR_GEMINI_API_KEY';
const genAI = new GoogleGenerativeAI(GEMINI_API_KEY);
const geminiModel = genAI.getGenerativeModel({ model: "gemini-1.5-flash" }); // Use gemini-1.5-flash or later

// --- Backend API Base URL (adjust if your backend is not on the same host) ---
const BACKEND_BASE_URL = '/api'; // Assumes backend functions are exposed at /api/goals, /api/achievements etc.

// --- Goals Management (Calls to backend) ---
export const getGoals = async (): Promise<Goal[]> => {
  // Fetch from backend endpoint, map response to Goal[]
  const response = await fetch(`${BACKEND_BASE_URL}/goals`);
  if (!response.ok) throw new Error('Failed to fetch goals');
  const { goals } = await response.json();
  return goals;
};
export const createGoal = async (newGoal: Omit<Goal, 'id' | 'currentStreak' | 'longestStreak' | 'createdAt'>): Promise<Goal> => {
  // Post to backend endpoint, map response to Goal
  const response = await fetch(`${BACKEND_BASE_URL}/goals`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(newGoal),
  });
  if (!response.ok) throw new Error('Failed to create goal');
  const { goal } = await response.json();
  return goal;
};
export const updateGoalProgress = async (goalId: string, currentValue: number): Promise<Goal> => {
  // Post to backend endpoint, map response to Goal
  const response = await fetch(`${BACKEND_BASE_URL}/goals/${goalId}/progress`, {
    method: 'PUT', // Or POST if REST convention is different
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ goalId, current_value: currentValue }), // Send snake_case to backend
  });
  if (!response.ok) throw new Error('Failed to update goal progress');
  const { goal } = await response.json();
  return goal;
};
export const updateDebt = async (goalId: string, debtAmount: number): Promise<Goal> => {
  // Post to backend endpoint, map response to Goal
  const response = await fetch(`${BACKEND_BASE_URL}/goals/${goalId}/debt`, {
    method: 'PUT', // Or POST
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ goalId, debt_amount: debtAmount }), // Send snake_case
  });
  if (!response.ok) throw new Error('Failed to update debt');
  const { goal } = await response.json();
  return goal;
};

// --- Daily Logs Management (localStorage) ---
export const getDailyLogs = async (): Promise<DailyLogEntry[]> => {
  // Get from localStorage, sort by date DESC, include mock data if empty
  return new Promise(resolve => { /* ... localStorage logic ... */ });
};
export const createDailyLog = async (newLog: Omit<DailyLogEntry, 'id' | 'timestamp'>): Promise<DailyLogEntry> => {
  // Save to localStorage, update if date exists, return new/updated entry
  return new Promise(resolve => { /* ... localStorage logic ... */ });
};
export const updateDailyLog = async (updatedLog: DailyLogEntry): Promise<DailyLogEntry> => {
  // Update in localStorage, return updated entry
  return new Promise((resolve, reject) => { /* ... localStorage logic ... */ });
};
export const getDailyLogByDate = async (date: string): Promise<DailyLogEntry | null> => {
  // Get specific date from localStorage
  return new Promise(resolve => { /* ... localStorage logic ... */ });
};

// --- Lumina Management (localStorage) ---
export const getLumina = async (): Promise<number> => {
  // Get from localStorage, default to 100 if empty
  return new Promise(resolve => { /* ... localStorage logic ... */ });
};
export const updateLumina = async (amount: number): Promise<number> => {
  // Adjust and save to localStorage
  return new Promise(resolve => { /* ... localStorage logic ... */ });
};
export const processTransactionsForLumina = async (transactions: BankTransaction[]): Promise<number> => {
  // Calculate net change, call updateLumina, return new balance
  return new Promise(resolve => { /* ... logic ... */ });
};

// --- Achievements Management (Mix of backend and localStorage for AI-generated) ---
export const getAchievements = async (goalId?: string): Promise<Achievement[]> => {
  // Fetch from backend endpoint (e.g., /api/achievements or /api/achievements?goalId=...)
  // Optionally combine with localStorage for AI-generated ones not yet saved to DB
  const response = await fetch(`${BACKEND_BASE_URL}/achievements${goalId ? `?goalId=${goalId}` : ''}`);
  if (!response.ok) throw new Error('Failed to fetch achievements');
  const { achievements } = await response.json();
  return achievements;
};

export const addAchievementFromAI = async (template: AchievementTemplate): Promise<Achievement> => {
  // Convert template to full Achievement, generate client-side ID, save to localStorage for now.
  // In a real app, this might also trigger a backend call to save to DB.
  return new Promise(resolve => { /* ... localStorage logic ... */ });
};


// --- AI Assistant Integration (Gemini API Call) ---
export const processAIRequest = async (userInput: string): Promise<AIResponse> => {
  // IMPORTANT: Craft comprehensive prompt for Gemini
  // Prompt instructions for each AIResponse type:
  // 1. achievement_template: "name", "description", "condition", "category", "linkedQuestName" (string | null)
  // 2. quest_suggestion: "name", "description", "category", "targetValue", "currentValue": 0, "isDebt", "debtAmount", "currentDebt", "dailyRepeatable": true, "unit"
  // 3. daily_stats_summary: "date", "hp", "mp", "status_ailments", "notes"
  // 4. daily_stats_trend: "query", "start_date", "end_date", "hp_average", "mp_average", "most_frequent_ailment", "days_with_ailments"
  // 5. daily_stats_comparison: "query", "compared_stat", "current_value", "compared_value", "is_higher", "difference"
  // 6. general_response: "message"
  // Always enclose JSON in ```json...```.
  // Use geminiModel.generateContent(prompt) and parse response.
  try { /* ... Gemini API call logic ... */ } catch (error) { /* ... error handling ... */ }
};
6. Core UI Components (src/components/)
Implement the following reusable React components using Tailwind CSS for a neon-cyberpunk aesthetic.

Layout.tsx: (As previously provided, with all navigation links and base styling)
Top navigation bar (desktop) and Bottom navigation bar (mobile).
Links: Dashboard, New Quest, Daily Ops Report, AI Assistant, Docs.
Overall base styling: bg-[#0a0a20], text-[#00ff9d], min-h-screen.
CyberButton.tsx: (Glowing, interactive button)
CyberInput.tsx: (Input field with neon borders)
CyberTextArea.tsx: (Text area with neon borders)
LoadingSpinner.tsx: (Simple loading animation)
HpBar.tsx: (Progress bar for HP/MP, visualizes current/max, with neon fill)
Icon Components (src/components/icons/):
HomeIcon.tsx (or default Font Awesome fa-home)
PlusIcon.tsx (or default Font Awesome fa-plus)
DocumentationIcon.tsx (or default Font Awesome fa-book)
QuestsIcon.tsx (or default Font Awesome fa-clipboard-list)
HeartPulseIcon.tsx (for Daily Ops)
SparkleIcon.tsx (for AI Assistant/Lumina)
LuminaIcon.tsx (Optional: if different from SparkleIcon, otherwise SparkleIcon works)
7. Main Pages (src/pages/)
Implement the following core application pages.

App.tsx: (As previously provided, sets up HashRouter and routes all pages to Layout)
index.tsx: (As previously provided, mounts the React app to div#root)
DashboardPage.tsx (/): Gamified Life Map (NEW)
Goal: Visually represent progress, stats, and Lumina on an interactive map.
Layout: Large, responsive SVG-based map.
Map Elements:
Progress Path/Trail: Winding path that visually extends/changes color based on total goal progress (or number of completed goals).
Nodes/Checkpoints: Stylized nodes along the path representing completed goals and unlocked achievements. These should glow brightly or show a completed icon.
Thematic Zones/Regions: 4 distinct, abstract, glowing SVG zones for:
"Knowledge Spire" (Learning): Grows/brightens with learning goals.
"Training Grid" (Fitness): Pulsates/vibrates with fitness goals.
"Lumina Vault" (Financial): Fills/glows brighter with Lumina balance.
"Tranquility Core" (Mental Well-being): Its aura reflects current HP/MP.
Character Avatar (Optional): A simple glowing marker on the path based on overall progress.
Dynamic Visual Cues:
Overall map aura color changes based on latest currentHP/currentMP (vibrant for high, subtle red for low).
Small status effect icons over relevant zones if ailments are active (e.g., glitch over Tranquility Core for "Fatigue").
Lumina Display: Prominent display of current Lumina balance using SparkleIcon (or LuminaIcon) and glowing text.
Interactivity:
Clicking a zone: Displays a simple modal/side panel showing quests/achievements in that category.
Hovering a node: Tooltip with completed quest/achievement name.
Data: Fetch goals, achievements, dailyLogs, lumina from services/api.ts.
DailyOpsReportPage.tsx (/daily-ops-report): Daily Stats & Lumina (NEW)
Goal: Central hub for viewing/logging daily HP, MP, Status Ailments, and managing Lumina.
Current Date Display: Prominently show "Daily Report for [Date]".
HP/MP Display: Two HpBar components for currentHP/maxHP and currentMP/maxMP, clearly labeled.
Status Ailments: A section listing active ailments for the selected day.
Lumina Balance: Display current Lumina balance.
Date Navigation: "Previous Day" and "Next Day" CyberButtons to browse historical DailyLogEntry data.
AI-Assisted Input Area:
CyberTextArea for natural language input (e.g., "Woke up feeling drained, HP 40. Headache. MP around 70.").
"Process with AI" CyberButton to send input to processAIRequest.
Display AI-parsed results (HP, MP, Ailments) into separate, manual CyberInput fields for user review/override.
"Log Today's Status" CyberButton to save to createDailyLog/updateDailyLog.
Lumina File Import (Automated Manual):
File input element (<input type="file" accept=".csv" />).
"Process Bank Statement" CyberButton.
Client-Side Parsing: Upon file upload, parse the CSV (assuming a common format like Date,Description,Amount or Date,Description,Debit,Credit).
Use processTransactionsForLumina to update Lumina based on the net change.
Display a confirmation message for Lumina updated.
History Log: A scrollable list of recent DailyLogEntry summaries (date, HP/MP snapshot, ailments).
Data: Fetch/update DailyLogEntry via services/api.ts.
NewQuestPage.tsx (/quests/new):
Form fields: Quest name, description, category, target value, is debt (checkbox), initial debt amount (if debt).
Submit button to create new goal using createGoal.
Cancel button.
UpdateGoalPage.tsx (/update/:goalId):
Accepts goalId from URL.
Displays current goal details.
Form to update currentValue (and debtAmount if isDebt).
Update button using updateGoalProgress or updateDebt.
Basic streak tracking visualization.
Health points display and Achievement progress related to this goal (placeholders for now).
AIAssistantPage.tsx (/ai-assistant): AI Command Terminal (NEW)
Goal: Interactive terminal for AI interactions.
Layout: CyberTextArea for user input, "Process Command" CyberButton.
AI Response Display: Displays structured AI responses based on AIResponse.type.
Quest Suggestions: If type is quest_suggestion:
Display proposed quest details (name, description, category, targetValue, isDebt, debtAmount).
"Add This Quest to My Goals" CyberButton using createGoal.
Achievement Templates: If type is achievement_template:
Display proposed achievement details (name, description, condition, category, linkedQuestName).
Dropdown to link to existing goals (getGoals used here to populate options).
"Add This Achievement to My Log" CyberButton using addAchievementFromAI.
Daily Stats Queries: If type is daily_stats_query, daily_stats_summary, daily_stats_trend, or daily_stats_comparison:
Display parsed query and relevant data points or summary.
(Note: Actual historical stat fetching for summary/trend/comparison would occur in this component using getDailyLogs/getDailyLogByDate and be displayed. AI just dictates the query format).
General Responses: Display a friendly message.
Loading spinner and error display.
DocumentationPage.tsx (/docs):
Placeholder content for guide, schemas, API endpoints.
8. Styling Guidelines (public/index.html & Tailwind Classes)
Global Styling (in public/index.html <style type="text/tailwindcss"> block):

Base Colors:
--bg-primary: #0a0a20;
--text-primary: #00ff9d;
--accent: #00ffff;
--card-bg: #1a1a35;
--hover: #2a2a45;
Custom Classes:
.shadow-cyberpunk: box-shadow: 0 0 15px rgba(0, 255, 157, 0.5), 0 0 30px rgba(0, 255, 255, 0.2);
.shadow-cyberpunk-inset: box-shadow: inset 0 0 10px rgba(0, 255, 157, 0.3), inset 0 0 20px rgba(0, 255, 255, 0.1);
.border-neon-green: border-color: #00ff9d;
.border-neon-cyan: border-color: #00ffff;
.text-neon-cyan: color: #00ffff;
.text-neon-green: color: #00ff9d;
.bg-card: background-color: #1a1a35;
.hover-glow:hover: box-shadow: 0 0 20px rgba(0, 255, 157, 0.6), 0 0 40px rgba(0, 255, 255, 0.3);
.shadow-text: text-shadow: 0 0 5px #00ffff, 0 0 10px #00ffff, 0 0 15px #00ffff;
.animate-fade-in: animation: fadeIn 0.5s ease-out; (define @keyframes fadeIn)
Component Specific Styles: Use these classes heavily throughout UI components.
Fonts: Use "Inter" as the primary sans-serif font.
Rounded Corners: Apply rounded-lg or similar to all major elements.
9. Lumina Currency (Lumina) Details
Name: "Lumina".
Display: Prominently displayed on Dashboard and Daily Ops Report pages.
Storage: Managed in localStorage under rpg_lumina_balance.
Initial Balance: Default to 100 Lumina if not found.
Automated File Import (Client-Side):
User uploads a CSV bank statement file (e.g., from Navy Federal, Bank of America).
Your frontend JavaScript parses the CSV to extract BankTransaction data (date, description, amount, type).
The processTransactionsForLumina function calculates the net change (deposits - withdrawals) and applies this to the user's Lumina balance via updateLumina.
Provide basic CSV parsing logic (e.g., split by lines, then by comma for columns). Make assumptions about column order (e.g., first column is date, last is amount). Handle both single-amount column (positive/negative) and separate debit/credit columns (prioritize separate if present).
10. AI Assistant (Gemini) Detailed Prompting
The processAIRequest function in src/services/api.ts will send user input to gemini-1.5-flash model. The prompt it sends to Gemini must explicitly instruct Gemini to return structured JSON for different intents, enclosed in markdown blocks.

Gemini Prompting Strategy (within processAIRequest):

You are an AI assistant for a "Level Up Your Life" RPG-themed goal tracking app.
Your goal is to help the user manage their life stats and create new gamified elements.
The user will provide a request. Analyze the request and respond in a structured JSON format.

Here's the data the app currently tracks (for context, do NOT invent data):
- Goals/Quests: id, name, description, category, currentValue, targetValue, streak, etc.
- Daily Logs: date, currentHP, maxHP, currentMP, maxMP, statusAilments, rawUserInput.
- Achievements: id, name, description, unlocked status, associated goalId.

Based on the user's input:

1.  **If the user asks to create a new achievement:**
    Respond with {"type": "achievement_template", "payload": {"name": "string", "description": "string", "condition": "string (natural language condition)", "category": "string (e.g., Health, Mental, Productivity, Quest-Specific)", "linkedQuestName": "string | null (Name of specific quest if inferred; otherwise null)"}}.
    Make the name and description compelling and RPG-themed.
    Example Achievement Condition: "Reach 100 HP", "Complete 'Study Code' quest 10 times", "Maintain MP above 80 for 5 consecutive days".

2.  **If the user asks for a new quest or goal suggestion (including daily workout quests):**
    Respond with {"type": "quest_suggestion", "payload": {
      "name": "string",
      "description": "string",
      "category": "string (e.g., Fitness, Learning, Financial, Creative, Social, Wellness)",
      "targetValue": "number (A sensible numerical target. Default to 1 if unitless like 'daily exercise' or 100 for skill XP.)",
      "currentValue": 0,
      "isDebt": "boolean (true if explicit debt/negative tracking, otherwise false)",
      "debtAmount": "number (optional, if isDebt is true)",
      "currentDebt": "number (optional, if isDebt is true, should be equal to debtAmount for new debt quests)",
      "dailyRepeatable": "boolean (true if explicitly for daily, repeatable tasks like workouts, otherwise false)",
      "unit": "string (Optional: 'miles', 'push-ups', 'sit-ups', 'reps', 'sets', 'minutes', 'sessions' for daily repeatable quests)"
    }}.
    Ensure the quest details are complete enough for a new Goal object.

3.  **If the user asks for a summary or query about their daily stats (HP, MP, ailments, etc.):**
    * **For single-day summaries (today, specific date):**
        Respond with {"type": "daily_stats_summary", "payload": {"date": "string (YYYY-MM-DD)", "hp": "number (currentHP)", "mp": "number (currentMP)", "status_ailments": ["string"], "notes": "string"}, "message": "string (A concise summary)"}.
    * **For multi-day summaries or trends (this week, last 3 days, etc.):**
        Respond with {"type": "daily_stats_trend", "payload": {"query": "string (original query)", "start_date": "string (YYYY-MM-DD)", "end_date": "string (YYYY-MM-DD)", "hp_average": "number", "mp_average": "number", "most_frequent_ailment": "string | 'None'", "days_with_ailments": "number"}, "message": "string (A concise trend summary)"}.
    * **For comparisons (higher than yesterday, etc.):**
        Respond with {"type": "daily_stats_comparison", "payload": {"query": "string (original query)", "compared_stat": "string ('HP' or 'MP')", "current_value": "number", "compared_value": "number", "is_higher": "boolean", "difference": "number"}, "message": "string (A concise comparison)"}.
    (Note: The actual data retrieval for stats will happen on the frontend using existing API functions; the AI just helps identify the query and structure.)

4.  **For any other general question or command:**
    Respond with {"type": "general_response", "message": "string (a helpful, encouraging, or informational response)"}.

Always enclose the JSON in a markdown block (```json...```). Keep responses concise and directly to the point. If a numerical value is implied but not exact, use a reasonable default (e.g., 100 for a target value).

User Request: "${userInput}"
Final Instruction to You (the AI building the app):

Generate all the necessary files and code (HTML, TypeScript/React, backend handlers, CSS) for this entire application. Ensure all components are complete, self-contained, and adhere to the specified styling and functionality. Provide clear comments for all code. Implement the base structure and then fill in the logic for each page and component, focusing on the interactions described above. Assume npm for package management.

Start by providing the complete public/index.html and package.json for the base setup, followed by the rest of the app's files sequentially.