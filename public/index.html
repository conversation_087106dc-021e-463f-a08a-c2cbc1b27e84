<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Level Up Your Life</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css" integrity="sha512-SnH5WK+bZxgPHs44uWIX+LLJAJ9/2PkPKZ5QiAj6Ta86w+fsb2TkcmfRyVX3pBnMFcV7oQPJkl9QevSCWr3W6A==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <style type="text/tailwindcss">
      @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap');

      @layer base {
        :root {
          --bg-primary: #0a0a20;
          --text-primary: #00ff9d;
          --accent: #00ffff;
          --card-bg: #1a1a35;
          --hover: #2a2a45;
        }
        body {
          @apply bg-[--bg-primary] text-[--text-primary] min-h-screen font-sans;
          font-family: 'Inter', sans-serif;
        }
      }

      @layer components {
        .shadow-cyberpunk {
          box-shadow: 0 0 15px rgba(0, 255, 157, 0.5), 0 0 30px rgba(0, 255, 255, 0.2);
        }
        .shadow-cyberpunk-inset {
          box-shadow: inset 0 0 10px rgba(0, 255, 157, 0.3), inset 0 0 20px rgba(0, 255, 255, 0.1);
        }
        .border-neon-green {
          border-color: #00ff9d;
        }
        .border-neon-cyan {
          border-color: #00ffff;
        }
        .text-neon-cyan {
          color: #00ffff;
        }
        .text-neon-green {
          color: #00ff9d;
        }
        .bg-card {
          background-color: #1a1a35;
        }
        .hover-glow:hover {
          box-shadow: 0 0 20px rgba(0, 255, 157, 0.6), 0 0 40px rgba(0, 255, 255, 0.3);
        }
        .shadow-text {
          text-shadow: 0 0 5px #00ffff, 0 0 10px #00ffff, 0 0 15px #00ffff;
        }
        .animate-fade-in {
          animation: fadeIn 0.5s ease-out;
        }
      }

      @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
      }
    </style>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/index.tsx"></script>
  </body>
</html>
