import { sql } from '@vercel/postgres';
import { Goal } from '../types'; // Adjust path as needed

// Helper function to map database snake_case to frontend camelCase
const mapGoalToCamelCase = (goal: any): Goal => ({
  id: goal.id,
  name: goal.name,
  description: goal.description,
  category: goal.category,
  currentValue: goal.current_value,
  targetValue: goal.target_value,
  isDebt: goal.is_debt,
  debtAmount: goal.debt_amount,
  currentStreak: goal.current_streak,
  longestStreak: goal.longest_streak,
  createdAt: new Date(goal.created_at).getTime(), // Convert timestamp to Unix timestamp
  dailyRepeatable: goal.daily_repeatable,
  unit: goal.unit,
});

// Define request body types for clarity
interface CreateGoalRequestBody {
  name: string;
  description?: string;
  category: string;
  target_value: number;
  is_debt?: boolean;
  debt_amount?: number;
  daily_repeatable?: boolean;
  unit?: string;
}

interface UpdateGoalProgressRequestBody {
  goalId: string;
  current_value: number;
}

// getGoalsHandler function
export async function getGoalsHandler(): Promise<{ goals: Goal[] }> {
  try {
    const result = await sql`SELECT * FROM goals ORDER BY created_at DESC;`;
    const goals = result.rows.map(mapGoalToCamelCase);
    return { goals };
  } catch (error) {
    console.error('Error fetching goals:', error);
    throw new Error('Failed to fetch goals');
  }
}

// createGoalHandler function
export async function createGoalHandler(body: CreateGoalRequestBody): Promise<{ goal: Goal }> {
  try {
    const { name, description, category, target_value, is_debt = false, debt_amount = 0, daily_repeatable = false, unit } = body;
    const result = await sql`
      INSERT INTO goals (name, description, category, target_value, is_debt, debt_amount, daily_repeatable, unit)
      VALUES (${name}, ${description}, ${category}, ${target_value}, ${is_debt}, ${debt_amount}, ${daily_repeatable}, ${unit})
      RETURNING *;
    `;
    const goal = mapGoalToCamelCase(result.rows[0]);
    return { goal };
  } catch (error) {
    console.error('Error creating goal:', error);
    throw new Error('Failed to create goal');
  }
}

// updateGoalProgressHandler function
export async function updateGoalProgressHandler(body: UpdateGoalProgressRequestBody): Promise<{ goal: Goal }> {
  try {
    const { goalId, current_value } = body;
    const result = await sql`
      UPDATE goals
      SET current_value = ${current_value}
      WHERE id = ${goalId}
      RETURNING *;
    `;
    if (result.rows.length === 0) {
      throw new Error('Goal not found');
    }
    const goal = mapGoalToCamelCase(result.rows[0]);
    return { goal };
  } catch (error) {
    console.error('Error updating goal progress:', error);
    throw new Error('Failed to update goal progress');
  }
}
