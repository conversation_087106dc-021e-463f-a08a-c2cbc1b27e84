import { sql } from '@vercel/postgres';
import { Achievement } from '../types'; // Adjust path as needed

// Helper function to map database snake_case to frontend camelCase
const mapAchievementToCamelCase = (achievement: any): Achievement => ({
  id: achievement.id,
  goalId: achievement.goal_id,
  name: achievement.name,
  description: achievement.description,
  iconClass: achievement.icon_class,
  requirementValue: achievement.requirement_value,
  unlocked: achievement.unlocked,
  unlockedAt: achievement.unlocked_at ? new Date(achievement.unlocked_at).getTime() : undefined, // Convert timestamp to Unix timestamp, handle null
  createdAt: new Date(achievement.created_at).getTime(), // Convert timestamp to Unix timestamp
});

// Define request body type
interface GetAchievementsRequestBody {
  goalId?: string; // goalId is optional for global achievements
}

// getAchievementsHandler function
export async function getAchievementsHandler(body?: GetAchievementsRequestBody): Promise<{ achievements: Achievement[] }> {
  try {
    const goalId = body?.goalId;
    let result;
    if (goalId) {
      result = await sql`SELECT * FROM achievements WHERE goal_id = ${goalId} ORDER BY requirement_value ASC;`;
    } else {
      result = await sql`SELECT * FROM achievements ORDER BY requirement_value ASC;`;
    }
    const achievements = result.rows.map(mapAchievementToCamelCase);
    return { achievements };
  } catch (error) {
    console.error('Error fetching achievements:', error);
    throw new Error('Failed to fetch achievements');
  }
}

// (Optional, if needed for direct achievement creation, not just AI template conversion)
// createAchievementHandler function
// export async function createAchievementHandler(body: CreateAchievementRequestBody): Promise<{ achievement: Achievement }> { ... }
