import { sql } from '@vercel/postgres';
import { Goal } from '../types'; // Adjust path as needed

// Helper function to map database snake_case to frontend camelCase
const mapGoalToCamelCase = (goal: any): Goal => ({
  id: goal.id,
  name: goal.name,
  description: goal.description,
  category: goal.category,
  currentValue: goal.current_value,
  targetValue: goal.target_value,
  isDebt: goal.is_debt,
  debtAmount: goal.debt_amount,
  currentStreak: goal.current_streak,
  longestStreak: goal.longest_streak,
  createdAt: new Date(goal.created_at).getTime(), // Convert timestamp to Unix timestamp
  dailyRepeatable: goal.daily_repeatable,
  unit: goal.unit,
});

// Define request body type
interface UpdateDebtRequestBody {
  goalId: string;
  debt_amount: number;
}

// updateDebtHandler function
export async function updateDebtHandler(body: UpdateDebtRequestBody): Promise<{ goal: Goal }> {
  try {
    const { goalId, debt_amount } = body;
    const result = await sql`
      UPDATE goals
      SET debt_amount = ${debt_amount}
      WHERE id = ${goalId} AND is_debt = true
      RETURNING *;
    `;
    if (result.rows.length === 0) {
      throw new Error('Debt goal not found or is not a debt goal');
    }
    const goal = mapGoalToCamelCase(result.rows[0]);
    return { goal };
  } catch (error) {
    console.error('Error updating debt:', error);
    throw new Error('Failed to update debt');
  }
}
