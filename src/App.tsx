import React from 'react';
import { HashRouter as Router, Routes, Route } from 'react-router-dom';
import { Helmet } from 'react-helmet';
import Layout from './components/Layout';
import DashboardPage from './pages/DashboardPage';
import NewQuestPage from './pages/NewQuestPage';
import UpdateGoalPage from './pages/UpdateGoalPage';
import DailyOpsReportPage from './pages/DailyOpsReportPage';
import AIAssistantPage from './pages/AIAssistantPage';
import DocumentationPage from './pages/DocumentationPage';
import { ROUTES } from './constants';

const App: React.FC = () => {
  return (
    <>
      <Helmet>
        <title>Level Up Your Life - RPG Goals App</title>
        <meta name="description" content="Gamify your personal growth with RPG-themed goal tracking, daily stats, and AI assistance." />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta name="theme-color" content="#00ff9d" />
      </Helmet>
      
      <Router>
        <Layout>
          <Routes>
            <Route path={ROUTES.DASHBOARD} element={<DashboardPage />} />
            <Route path={ROUTES.NEW_QUEST} element={<NewQuestPage />} />
            <Route path={ROUTES.UPDATE_GOAL} element={<UpdateGoalPage />} />
            <Route path={ROUTES.DAILY_OPS_REPORT} element={<DailyOpsReportPage />} />
            <Route path={ROUTES.AI_ASSISTANT} element={<AIAssistantPage />} />
            <Route path={ROUTES.DOCS} element={<DocumentationPage />} />
            
            {/* Fallback route */}
            <Route path="*" element={<DashboardPage />} />
          </Routes>
        </Layout>
      </Router>
    </>
  );
};

export default App;
