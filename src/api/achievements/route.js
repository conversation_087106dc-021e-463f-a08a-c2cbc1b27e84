async function handler() {
  const achievements = await sql`
    SELECT 
      a.*,
      lg.name as goal_name,
      lg.current_value,
      CASE 
        WHEN lg.current_value >= a.requirement_value THEN true
        ELSE false
      END as is_unlocked
    FROM achievements a
    LEFT JOIN life_goals lg ON a.goal_id = lg.id
    ORDER BY a.id ASC
  `;

  return { achievements };
}
export async function POST(request) {
  return handler(await request.json());
}