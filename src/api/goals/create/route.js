async function handler({
  category,
  name,
  description,
  targetValue,
  isDebt = false,
  debtAmount = 0,
  achievements = [],
}) {
  if (!category || !name || !targetValue) {
    return { error: "Category, name, and target value are required" };
  }

  return await sql.transaction(async (txn) => {
    const [goal] = await txn`
      INSERT INTO life_goals 
      (category, name, description, target_value, current_value, is_debt, debt_amount)
      VALUES 
      (${category}, ${name}, ${description}, ${targetValue}, ${
      isDebt ? debtAmount : 0
    }, ${isDebt}, ${debtAmount})
      RETURNING *
    `;

    if (achievements.length > 0) {
      await txn`
        INSERT INTO achievements 
        (name, description, icon_class, requirement_value, goal_id)
        SELECT 
          a.name,
          a.description,
          a.icon_class,
          a.requirement_value,
          ${goal.id}
        FROM unnest(${achievements}::achievement[]) as a
      `;
    }

    await txn`
      INSERT INTO streaks 
      (goal_id, current_streak, longest_streak)
      VALUES 
      (${goal.id}, 0, 0)
    `;

    const [newGoal] = await txn`
      SELECT 
        lg.*,
        COALESCE(s.current_streak, 0) as current_streak,
        COALESCE(s.longest_streak, 0) as longest_streak
      FROM life_goals lg
      LEFT JOIN streaks s ON lg.id = s.goal_id
      WHERE lg.id = ${goal.id}
    `;

    return { goal: newGoal };
  });
}
export async function POST(request) {
  return handler(await request.json());
}