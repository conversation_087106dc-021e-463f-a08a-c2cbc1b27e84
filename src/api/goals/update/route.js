async function handler({ goalId, value, notes }) {
  if (!goalId || value === undefined) {
    return { error: "Goal ID and value are required" };
  }

  return await sql.transaction(async (sql) => {
    const now = new Date();

    // Log the progress
    await sql`
      INSERT INTO daily_logs 
      (goal_id, value, notes, logged_at)
      VALUES 
      (${goalId}, ${value}, ${notes}, ${now})
    `;

    // Update goal current value
    await sql`
      UPDATE life_goals 
      SET current_value = current_value + ${value},
          updated_at = ${now}
      WHERE id = ${goalId}
    `;

    // Handle streak logic
    const [streak] = await sql`
      SELECT * FROM streaks WHERE goal_id = ${goalId}
    `;

    if (!streak) {
      await sql`
        INSERT INTO streaks (goal_id, current_streak, longest_streak, last_logged_at)
        VALUES (${goalId}, 1, 1, ${now})
      `;
    } else {
      const lastLog = new Date(streak.last_logged_at);
      const isYesterday = now.getDate() - lastLog.getDate() === 1;
      const isToday = now.getDate() === lastLog.getDate();

      let newCurrentStreak = isYesterday
        ? streak.current_streak + 1
        : isToday
        ? streak.current_streak
        : 1;

      let newLongestStreak = Math.max(newCurrentStreak, streak.longest_streak);

      await sql`
        UPDATE streaks 
        SET current_streak = ${newCurrentStreak},
            longest_streak = ${newLongestStreak},
            last_logged_at = ${now}
        WHERE goal_id = ${goalId}
      `;
    }

    // Return updated goal data
    const [updatedGoal] = await sql`
      SELECT 
        lg.*,
        COALESCE(s.current_streak, 0) as current_streak,
        COALESCE(s.longest_streak, 0) as longest_streak
      FROM life_goals lg
      LEFT JOIN streaks s ON lg.id = s.goal_id
      WHERE lg.id = ${goalId}
    `;

    return { goal: updatedGoal };
  });
}
export async function POST(request) {
  return handler(await request.json());
}