async function handler({ goalId, debtAmount }) {
  if (!goalId || debtAmount === undefined) {
    return { error: "Goal ID and debt amount are required" };
  }

  return await sql.transaction(async (txn) => {
    const [goal] = await txn`
      SELECT * FROM life_goals 
      WHERE id = ${goalId}
    `;

    if (!goal) {
      return { error: "Goal not found" };
    }

    if (!goal.is_debt) {
      return { error: "This goal is not marked as a debt goal" };
    }

    await txn`
      UPDATE life_goals 
      SET debt_amount = ${debtAmount},
          updated_at = CURRENT_TIMESTAMP
      WHERE id = ${goalId}
    `;

    const [updatedGoal] = await txn`
      SELECT 
        lg.*,
        COALESCE(s.current_streak, 0) as current_streak,
        COALESCE(s.longest_streak, 0) as longest_streak
      FROM life_goals lg
      LEFT JOIN streaks s ON lg.id = s.goal_id
      WHERE lg.id = ${goalId}
    `;

    return { goal: updatedGoal };
  });
}
export async function POST(request) {
  return handler(await request.json());
}