async function handler() {
  const achievements = await sql`
    SELECT 
      a.*,
      lg.name as goal_name,
      lg.current_value,
      CASE 
        WHEN a.unlocked_at IS NOT NULL THEN true
        ELSE false
      END as is_unlocked,
      CASE
        WHEN a.requirement_value IS NOT NULL THEN 
          COALESCE(lg.current_value / NULLIF(a.requirement_value, 0) * 100, 0)
        ELSE 0
      END as progress_percentage
    FROM achievements a
    LEFT JOIN life_goals lg ON a.goal_id = lg.id
    ORDER BY a.unlocked_at DESC NULLS LAST, a.id ASC
  `;

  return { achievements };
}
export async function POST(request) {
  return handler(await request.json());
}