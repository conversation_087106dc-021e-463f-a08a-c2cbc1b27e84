async function handler({ goalId, value, notes }) {
  if (!goalId || value === undefined) {
    return { error: "Goal ID and value are required" };
  }

  const now = new Date();

  return await sql.transaction(async (txn) => {
    const [goal] = await txn`
      SELECT * FROM life_goals WHERE id = ${goalId}
    `;

    if (!goal) {
      return { error: "Goal not found" };
    }

    await txn`
      INSERT INTO daily_logs (goal_id, value, notes, logged_at)
      VALUES (${goalId}, ${value}, ${notes}, ${now})
    `;

    await txn`
      UPDATE life_goals 
      SET current_value = current_value + ${value},
          updated_at = ${now}
      WHERE id = ${goalId}
    `;

    const [streak] = await txn`
      SELECT * FROM streaks WHERE goal_id = ${goalId}
    `;

    const lastLog = streak?.last_logged_at
      ? new Date(streak.last_logged_at)
      : null;
    const isYesterday = lastLog
      ? now.getDate() - lastLog.getDate() === 1
      : false;
    const isToday = lastLog ? now.getDate() === lastLog.getDate() : false;

    const newCurrentStreak = !streak
      ? 1
      : isYesterday
      ? streak.current_streak + 1
      : isToday
      ? streak.current_streak
      : 1;
    const newLongestStreak = !streak
      ? 1
      : Math.max(newCurrentStreak, streak.longest_streak);

    if (!streak) {
      await txn`
        INSERT INTO streaks (goal_id, current_streak, longest_streak, last_logged_at)
        VALUES (${goalId}, ${newCurrentStreak}, ${newLongestStreak}, ${now})
      `;
    } else {
      await txn`
        UPDATE streaks 
        SET current_streak = ${newCurrentStreak},
            longest_streak = ${newLongestStreak},
            last_logged_at = ${now}
        WHERE goal_id = ${goalId}
      `;
    }

    const [updatedGoal] = await txn`
      SELECT 
        lg.*,
        COALESCE(s.current_streak, 0) as current_streak,
        COALESCE(s.longest_streak, 0) as longest_streak
      FROM life_goals lg
      LEFT JOIN streaks s ON lg.id = s.goal_id
      WHERE lg.id = ${goalId}
    `;

    const achievements = await txn`
      SELECT * FROM achievements 
      WHERE goal_id = ${goalId} 
      AND requirement_value <= ${updatedGoal.current_value}
      AND unlocked_at IS NULL
    `;

    if (achievements.length > 0) {
      await txn`
        UPDATE achievements 
        SET unlocked_at = ${now}
        WHERE goal_id = ${goalId} 
        AND requirement_value <= ${updatedGoal.current_value}
        AND unlocked_at IS NULL
      `;
    }

    return {
      goal: updatedGoal,
      unlockedAchievements: achievements,
    };
  });
}
export async function POST(request) {
  return handler(await request.json());
}