import React from 'react';

interface CyberInputProps {
  type?: 'text' | 'number' | 'email' | 'password' | 'file';
  placeholder?: string;
  value?: string | number;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  onFocus?: (e: React.FocusEvent<HTMLInputElement>) => void;
  disabled?: boolean;
  required?: boolean;
  className?: string;
  label?: string;
  error?: string;
  accept?: string; // For file inputs
}

const CyberInput: React.FC<CyberInputProps> = ({
  type = 'text',
  placeholder,
  value,
  onChange,
  onBlur,
  onFocus,
  disabled = false,
  required = false,
  className = '',
  label,
  error,
  accept,
}) => {
  const inputClasses = `
    w-full px-4 py-3 bg-transparent border-2 border-neon-green/50 rounded-lg
    text-neon-green placeholder-neon-green/50 
    focus:border-neon-green focus:outline-none focus:shadow-cyberpunk-inset
    transition-all duration-300
    ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
    ${error ? 'border-red-500 text-red-400' : ''}
    ${className}
  `.trim();

  return (
    <div className="space-y-2">
      {label && (
        <label className="block text-sm font-medium text-neon-cyan">
          {label}
          {required && <span className="text-red-400 ml-1">*</span>}
        </label>
      )}
      
      <input
        type={type}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        onBlur={onBlur}
        onFocus={onFocus}
        disabled={disabled}
        required={required}
        accept={accept}
        className={inputClasses}
      />
      
      {error && (
        <p className="text-red-400 text-sm mt-1">
          <i className="fas fa-exclamation-triangle mr-1"></i>
          {error}
        </p>
      )}
    </div>
  );
};

export default CyberInput;
