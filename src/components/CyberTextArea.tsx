import React from 'react';

interface CyberTextAreaProps {
  placeholder?: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLTextAreaElement>) => void;
  onFocus?: (e: React.FocusEvent<HTMLTextAreaElement>) => void;
  disabled?: boolean;
  required?: boolean;
  rows?: number;
  className?: string;
  label?: string;
  error?: string;
}

const CyberTextArea: React.FC<CyberTextAreaProps> = ({
  placeholder,
  value,
  onChange,
  onBlur,
  onFocus,
  disabled = false,
  required = false,
  rows = 4,
  className = '',
  label,
  error,
}) => {
  const textAreaClasses = `
    w-full px-4 py-3 bg-transparent border-2 border-neon-green/50 rounded-lg
    text-neon-green placeholder-neon-green/50 
    focus:border-neon-green focus:outline-none focus:shadow-cyberpunk-inset
    transition-all duration-300 resize-vertical
    ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
    ${error ? 'border-red-500 text-red-400' : ''}
    ${className}
  `.trim();

  return (
    <div className="space-y-2">
      {label && (
        <label className="block text-sm font-medium text-neon-cyan">
          {label}
          {required && <span className="text-red-400 ml-1">*</span>}
        </label>
      )}
      
      <textarea
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        onBlur={onBlur}
        onFocus={onFocus}
        disabled={disabled}
        required={required}
        rows={rows}
        className={textAreaClasses}
      />
      
      {error && (
        <p className="text-red-400 text-sm mt-1">
          <i className="fas fa-exclamation-triangle mr-1"></i>
          {error}
        </p>
      )}
    </div>
  );
};

export default CyberTextArea;
