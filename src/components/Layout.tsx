import React, { ReactNode } from 'react';
import { Link } from 'react-router-dom';
import { Helmet } from 'react-helmet';
import {
  FaHome,
  FaPlus,
  FaBook,
  FaClipboardList,
  FaHeartPulse,
  FaStar,
} from 'react-icons/fa6'; // Using react-icons for Font Awesome

interface LayoutProps {
  children: ReactNode;
  title: string;
}

const Layout: React.FC<LayoutProps> = ({ children, title }) => {
  return (
    <div className="min-h-screen bg-[--bg-primary] text-[--text-primary] font-sans flex flex-col">
      <Helmet>
        <title>{title} | Level Up Your Life</title>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet" />
        {/* Font Awesome CDN is already in index.html, but react-icons is used here */}
      </Helmet>

      {/* Top Navigation Bar (Desktop) */}
      <header className="hidden md:flex justify-between items-center p-4 shadow-cyberpunk bg-card">
        <h1 className="text-2xl font-bold text-neon-cyan shadow-text">Level Up Your Life</h1>
        <nav>
          <ul className="flex space-x-6">
            <li>
              <Link to="/" className="text-neon-green hover:text-neon-cyan transition-colors duration-200 hover-glow p-2 rounded-lg">
                <FaHome className="inline-block mr-1" /> Dashboard
              </Link>
            </li>
            <li>
              <Link to="/quests/new" className="text-neon-green hover:text-neon-cyan transition-colors duration-200 hover-glow p-2 rounded-lg">
                <FaPlus className="inline-block mr-1" /> New Quest
              </Link>
            </li>
             <li>
              <Link to="/daily-ops-report" className="text-neon-green hover:text-neon-cyan transition-colors duration-200 hover-glow p-2 rounded-lg">
                <FaHeartPulse className="inline-block mr-1" /> Daily Ops Report
              </Link>
            </li>
             <li>
              <Link to="/ai-assistant" className="text-neon-green hover:text-neon-cyan transition-colors duration-200 hover-glow p-2 rounded-lg">
                <FaStar className="inline-block mr-1" /> AI Assistant
              </Link>
            </li>
            <li>
              <Link to="/docs" className="text-neon-green hover:text-neon-cyan transition-colors duration-200 hover-glow p-2 rounded-lg">
                <FaBook className="inline-block mr-1" /> Docs
              </Link>
            </li>
          </ul>
        </nav>
      </header>

      {/* Main Content Area */}
      <main className="flex-grow container mx-auto p-4 md:p-8">
        {children}
      </main>

      {/* Bottom Navigation Bar (Mobile) */}
      <footer className="md:hidden fixed bottom-0 left-0 right-0 bg-card shadow-cyberpunk p-3">
        <nav>
          <ul className="flex justify-around text-sm">
             <li>
              <Link to="/" className="flex flex-col items-center text-neon-green hover:text-neon-cyan transition-colors duration-200">
                <FaHome className="text-xl" />
                <span>Dashboard</span>
              </Link>
            </li>
            <li>
              <Link to="/quests/new" className="flex flex-col items-center text-neon-green hover:text-neon-cyan transition-colors duration-200">
                <FaPlus className="text-xl" />
                <span>New Quest</span>
              </Link>
            </li>
             <li>
              <Link to="/daily-ops-report" className="flex flex-col items-center text-neon-green hover:text-neon-cyan transition-colors duration-200">
                <FaHeartPulse className="text-xl" />
                <span>Daily Ops</span>
              </Link>
            </li>
             <li>
              <Link to="/ai-assistant" className="flex flex-col items-center text-neon-green hover:text-neon-cyan transition-colors duration-200">
                <FaStar className="text-xl" />
                <span>AI</span>
              </Link>
            </li>
            <li>
              <Link to="/docs" className="flex flex-col items-center text-neon-green hover:text-neon-cyan transition-colors duration-200">
                <FaBook className="text-xl" />
                <span>Docs</span>
              </Link>
            </li>
          </ul>
        </nav>
      </footer>
    </div>
  );
};

export default Layout;
