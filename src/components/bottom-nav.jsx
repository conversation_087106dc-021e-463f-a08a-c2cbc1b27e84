"use client";
import React from "react";



export default function Index() {
  return (function MainComponent({ currentPath = "/" }) {
  const navItems = [
    {
      path: "/",
      label: "Home",
      icon: "fa-house",
    },
    {
      path: "/quests",
      label: "Quests",
      icon: "fa-scroll",
    },
    {
      path: "/debt",
      label: "Debt",
      icon: "fa-coins",
    },
    {
      path: "/update",
      label: "Update",
      icon: "fa-arrow-up",
    },
  ];

  return (
    <nav className="fixed bottom-0 left-0 right-0 bg-[#1a1a35] border-t border-[#00ff9d] shadow-[0_0_15px_rgba(0,255,157,0.3)] px-2 py-1">
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-around items-center">
          {navItems.map((item) => (
            <a
              key={item.path}
              href={item.path}
              className={`flex flex-col items-center py-2 px-3 min-w-[64px] ${
                currentPath === item.path
                  ? "text-[#00ff9d]"
                  : "text-[#00ff9d] opacity-50"
              }`}
            >
              <i
                className={`fas ${item.icon} text-xl mb-1 transition-colors`}
              ></i>
              <span className="text-xs font-medium">{item.label}</span>
            </a>
          ))}
        </div>
      </div>
    </nav>
  );
}

function StoryComponent() {
  return (
    <div className="min-h-screen bg-[#0a0a20] p-4">
      <div className="mb-8">
        <h2 className="text-[#00ff9d] text-xl mb-4">Home Selected</h2>
        <MainComponent currentPath="/" />
      </div>

      <div className="mb-8">
        <h2 className="text-[#00ff9d] text-xl mb-4">Quests Selected</h2>
        <MainComponent currentPath="/quests" />
      </div>

      <div className="mb-8">
        <h2 className="text-[#00ff9d] text-xl mb-4">Debt Selected</h2>
        <MainComponent currentPath="/debt" />
      </div>

      <div className="mb-8">
        <h2 className="text-[#00ff9d] text-xl mb-4">Update Selected</h2>
        <MainComponent currentPath="/update" />
      </div>
    </div>
  );
});
}