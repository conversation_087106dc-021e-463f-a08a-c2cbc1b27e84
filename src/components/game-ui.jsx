"use client";
import React from "react";



export default function Index() {
  return (function MainComponent({
  health = 100,
  maxHealth = 100,
  currentLocation = "Home",
}) {
  const healthPercentage = (health / maxHealth) * 100;
  const isLowHealth = healthPercentage <= 30;

  return (
    <div className="fixed top-0 left-0 right-0 bg-[#1a1a35] border-b-2 border-[#00ff9d] shadow-[0_0_15px_rgba(0,255,157,0.3)] font-roboto">
      <div className="max-w-7xl mx-auto p-4">
        <div className="flex justify-between items-center">
          <div className="w-64">
            <div className="flex items-center gap-2 mb-1">
              <span
                className={`text-[#ff0066] ${isLowHealth ? "health-text-glitch" : ""}`}
              >
                HP
              </span>
              <span
                className={`text-[#ff0066] ${isLowHealth ? "health-text-glitch" : ""}`}
              >
                {health}/{maxHealth}
              </span>
            </div>
            <div className="h-4 bg-[#2a2a45] rounded-full overflow-hidden shadow-[0_0_10px_rgba(255,0,102,0.3)]">
              <div
                className={`h-full bg-[#ff0066] transition-all duration-300 ${isLowHealth ? "health-bar-glitch" : ""}`}
                style={{ width: `${healthPercentage}%` }}
              />
            </div>
          </div>

          <nav className="flex items-center gap-6">
            <a
              href="/quests"
              className="text-[#00ff9d] hover:text-[#00ffff] transition-colors"
            >
              <i className="fas fa-scroll mr-2"></i>
              Quests
            </a>
            <a
              href="/debt"
              className="text-[#00ff9d] hover:text-[#00ffff] transition-colors"
            >
              <i className="fas fa-coins mr-2"></i>
              Debt
            </a>
            <a
              href="/update"
              className="text-[#00ff9d] hover:text-[#00ffff] transition-colors"
            >
              <i className="fas fa-chart-line mr-2"></i>
              Progress
            </a>
          </nav>

          <div className="text-[#00ffff]">
            <i className="fas fa-map-marker-alt mr-2"></i>
            {currentLocation}
          </div>
        </div>
      </div>

      <style jsx global>{`
        @keyframes healthBarGlitch {
          0% {
            transform: translateX(0);
            opacity: 1;
          }
          2% {
            transform: translateX(-2px);
            opacity: 0.8;
          }
          4% {
            transform: translateX(2px);
            opacity: 0.9;
          }
          6% {
            transform: translateX(-2px);
            opacity: 1;
          }
          8% {
            transform: translateX(0);
            opacity: 0.9;
          }
          100% {
            transform: translateX(0);
            opacity: 1;
          }
        }

        @keyframes textGlitch {
          0% {
            text-shadow: none;
            transform: translateX(0);
          }
          2% {
            text-shadow: -2px 0 #0ff;
            transform: translateX(1px);
          }
          4% {
            text-shadow: 2px 0 #f0f;
            transform: translateX(-1px);
          }
          6% {
            text-shadow: none;
            transform: translateX(0);
          }
        }

        .health-bar-glitch {
          animation: healthBarGlitch 2s infinite;
          animation-timing-function: steps(1, end);
        }

        .health-text-glitch {
          animation: textGlitch 1.5s infinite;
          animation-timing-function: steps(1, end);
        }
      `}</style>
    </div>
  );
}

function StoryComponent() {
  return (
    <div className="min-h-screen bg-[#0a0a20] p-6">
      <MainComponent health={75} maxHealth={100} currentLocation="Quest Hub" />
      <div className="mt-24">
        <h2 className="text-[#00ff9d] mb-8">Different States:</h2>

        <div className="space-y-8">
          <div>
            <h3 className="text-[#00ffff] mb-4">Full Health</h3>
            <MainComponent
              health={100}
              maxHealth={100}
              currentLocation="Home"
            />
          </div>

          <div>
            <h3 className="text-[#00ffff] mb-4">Low Health</h3>
            <MainComponent
              health={20}
              maxHealth={100}
              currentLocation="Dungeon"
            />
          </div>

          <div>
            <h3 className="text-[#00ffff] mb-4">Custom Max Health</h3>
            <MainComponent
              health={150}
              maxHealth={200}
              currentLocation="Training Ground"
            />
          </div>
        </div>
      </div>
    </div>
  );
});
}