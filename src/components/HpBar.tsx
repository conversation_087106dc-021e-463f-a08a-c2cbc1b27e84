import React from 'react';

interface HpBarProps {
  current: number;
  max: number;
  label: string;
  type?: 'hp' | 'mp' | 'progress';
  className?: string;
  showNumbers?: boolean;
}

const HpBar: React.FC<HpBarProps> = ({
  current,
  max,
  label,
  type = 'progress',
  className = '',
  showNumbers = true,
}) => {
  const percentage = Math.min((current / max) * 100, 100);
  
  const getBarColor = () => {
    switch (type) {
      case 'hp':
        if (percentage > 70) return 'bg-green-500';
        if (percentage > 30) return 'bg-yellow-500';
        return 'bg-red-500';
      case 'mp':
        return 'bg-blue-500';
      default:
        return 'bg-neon-green';
    }
  };

  const getGlowColor = () => {
    switch (type) {
      case 'hp':
        if (percentage > 70) return 'shadow-[0_0_10px_rgba(34,197,94,0.6)]';
        if (percentage > 30) return 'shadow-[0_0_10px_rgba(234,179,8,0.6)]';
        return 'shadow-[0_0_10px_rgba(239,68,68,0.6)]';
      case 'mp':
        return 'shadow-[0_0_10px_rgba(59,130,246,0.6)]';
      default:
        return 'shadow-cyberpunk';
    }
  };

  const getIcon = () => {
    switch (type) {
      case 'hp':
        return 'fa-heart';
      case 'mp':
        return 'fa-bolt';
      default:
        return 'fa-star';
    }
  };

  return (
    <div className={`space-y-2 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <i className={`fas ${getIcon()} text-neon-cyan`}></i>
          <span className="text-neon-cyan font-medium">{label}</span>
        </div>
        {showNumbers && (
          <span className="text-neon-green text-sm font-mono">
            {current}/{max}
          </span>
        )}
      </div>
      
      <div className="relative">
        <div className="w-full bg-gray-800 rounded-full h-3 border border-neon-green/30">
          <div
            className={`h-full rounded-full transition-all duration-500 ${getBarColor()} ${getGlowColor()}`}
            style={{ width: `${percentage}%` }}
          ></div>
        </div>
        
        {/* Percentage overlay */}
        <div className="absolute inset-0 flex items-center justify-center">
          <span className="text-xs font-bold text-white drop-shadow-lg">
            {Math.round(percentage)}%
          </span>
        </div>
      </div>
    </div>
  );
};

export default HpBar;
