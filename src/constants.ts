// Route definitions
export const ROUTES = {
  DASHBOARD: '/',
  NEW_QUEST: '/quests/new',
  UPDATE_GOAL: '/update/:goalId',
  DAILY_OPS_REPORT: '/daily-ops-report',
  AI_ASSISTANT: '/ai-assistant',
  DOCS: '/docs',
} as const;

// localStorage Keys
export const STORAGE_KEYS = {
  DAILY_LOGS: 'rpg_daily_logs',
  LUMINA_BALANCE: 'rpg_lumina_balance',
  ACHIEVEMENTS: 'rpg_achievements',
} as const;

// Default values
export const DEFAULTS = {
  LUMINA_BALANCE: 100,
  MAX_HP: 100,
  MAX_MP: 100,
} as const;

// Goal categories
export const GOAL_CATEGORIES = [
  'Fitness',
  'Learning',
  'Financial',
  'Creative',
  'Social',
  'Wellness',
  'Productivity',
  'Health',
  'Mental',
] as const;

// Status ailments
export const STATUS_AILMENTS = [
  'Fatigue',
  'Stress',
  'Headache',
  'Anxiety',
  'Low Energy',
  'Overwhelmed',
  'Distracted',
  'Unmotivated',
  'Sick',
  'Insomnia',
] as const;
