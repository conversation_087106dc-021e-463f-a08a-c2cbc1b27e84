import React, { useState, useEffect } from 'react';
import { getGoals, getAchievements, getDailyLogs, getLumina } from '../services/api';
import { Goal, Achievement, DailyLogEntry } from '../types';
import LoadingSpinner from '../components/LoadingSpinner';

const DashboardPage: React.FC = () => {
  const [goals, setGoals] = useState<Goal[]>([]);
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [dailyLogs, setDailyLogs] = useState<DailyLogEntry[]>([]);
  const [lumina, setLumina] = useState<number>(0);
  const [loading, setLoading] = useState(true);
  const [selectedZone, setSelectedZone] = useState<string | null>(null);

  useEffect(() => {
    const loadData = async () => {
      try {
        const [goalsData, achievementsData, logsData, luminaData] = await Promise.all([
          getGoals(),
          getAchievements(),
          getDailyLogs(),
          getLumina(),
        ]);
        
        setGoals(goalsData);
        setAchievements(achievementsData);
        setDailyLogs(logsData);
        setLumina(luminaData);
      } catch (error) {
        console.error('Error loading dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <LoadingSpinner size="lg" text="Loading your life map..." />
      </div>
    );
  }

  const latestLog = dailyLogs[0];
  const completedGoals = goals.filter(goal => goal.currentValue >= goal.targetValue);
  const totalProgress = goals.length > 0 ? (goals.reduce((sum, goal) => sum + (goal.currentValue / goal.targetValue), 0) / goals.length) * 100 : 0;

  const getZoneGoals = (category: string) => {
    const categoryMap: { [key: string]: string[] } = {
      'Knowledge Spire': ['Learning', 'Productivity'],
      'Training Grid': ['Fitness', 'Health'],
      'Lumina Vault': ['Financial'],
      'Tranquility Core': ['Wellness', 'Mental', 'Social'],
    };
    
    return goals.filter(goal => categoryMap[category]?.includes(goal.category));
  };

  const getZoneColor = (zoneName: string) => {
    const zoneGoals = getZoneGoals(zoneName);
    const zoneProgress = zoneGoals.length > 0 ? 
      (zoneGoals.reduce((sum, goal) => sum + (goal.currentValue / goal.targetValue), 0) / zoneGoals.length) : 0;
    
    if (zoneProgress > 0.7) return '#00ff9d';
    if (zoneProgress > 0.4) return '#00ffff';
    return '#4a5568';
  };

  const getAuraColor = () => {
    if (!latestLog) return '#4a5568';
    const avgStat = (latestLog.currentHP + latestLog.currentMP) / (latestLog.maxHP + latestLog.maxMP);
    if (avgStat > 0.7) return '#00ff9d';
    if (avgStat > 0.4) return '#00ffff';
    return '#ef4444';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-4xl font-bold shadow-text">Life Map</h1>
        <p className="text-neon-cyan">Navigate your journey to greatness</p>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-card rounded-lg p-4 border border-neon-green/30">
          <div className="flex items-center space-x-2">
            <i className="fas fa-sparkles text-neon-cyan"></i>
            <span className="text-neon-cyan">Lumina</span>
          </div>
          <p className="text-2xl font-bold text-neon-green">{lumina}</p>
        </div>
        
        <div className="bg-card rounded-lg p-4 border border-neon-green/30">
          <div className="flex items-center space-x-2">
            <i className="fas fa-trophy text-neon-cyan"></i>
            <span className="text-neon-cyan">Completed</span>
          </div>
          <p className="text-2xl font-bold text-neon-green">{completedGoals.length}</p>
        </div>
        
        <div className="bg-card rounded-lg p-4 border border-neon-green/30">
          <div className="flex items-center space-x-2">
            <i className="fas fa-target text-neon-cyan"></i>
            <span className="text-neon-cyan">Active Quests</span>
          </div>
          <p className="text-2xl font-bold text-neon-green">{goals.length}</p>
        </div>
        
        <div className="bg-card rounded-lg p-4 border border-neon-green/30">
          <div className="flex items-center space-x-2">
            <i className="fas fa-chart-line text-neon-cyan"></i>
            <span className="text-neon-cyan">Progress</span>
          </div>
          <p className="text-2xl font-bold text-neon-green">{Math.round(totalProgress)}%</p>
        </div>
      </div>

      {/* Interactive Life Map */}
      <div className="bg-card rounded-lg p-6 border border-neon-green/30">
        <div className="relative w-full h-96 overflow-hidden rounded-lg" style={{ backgroundColor: '#0a0a20' }}>
          <svg
            viewBox="0 0 800 400"
            className="w-full h-full"
            style={{ filter: `drop-shadow(0 0 20px ${getAuraColor()})` }}
          >
            {/* Background Grid */}
            <defs>
              <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
                <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#00ff9d" strokeWidth="0.5" opacity="0.3"/>
              </pattern>
            </defs>
            <rect width="800" height="400" fill="url(#grid)" />
            
            {/* Progress Path */}
            <path
              d="M 50 350 Q 200 300 350 250 Q 500 200 650 150 Q 750 100 750 50"
              fill="none"
              stroke="#00ff9d"
              strokeWidth="4"
              strokeDasharray={`${totalProgress * 7} ${700 - (totalProgress * 7)}`}
              className="animate-pulse"
            />
            
            {/* Knowledge Spire */}
            <g
              onClick={() => setSelectedZone('Knowledge Spire')}
              className="cursor-pointer"
            >
              <polygon
                points="150,100 200,50 250,100 200,150"
                fill={getZoneColor('Knowledge Spire')}
                stroke="#00ffff"
                strokeWidth="2"
                opacity="0.7"
                className="hover:opacity-1 transition-opacity"
              />
              <text x="200" y="105" textAnchor="middle" fill="#00ffff" fontSize="12" fontWeight="bold">
                Knowledge
              </text>
              <text x="200" y="120" textAnchor="middle" fill="#00ffff" fontSize="12" fontWeight="bold">
                Spire
              </text>
            </g>
            
            {/* Training Grid */}
            <g
              onClick={() => setSelectedZone('Training Grid')}
              className="cursor-pointer"
            >
              <rect
                x="450"
                y="200"
                width="100"
                height="100"
                fill={getZoneColor('Training Grid')}
                stroke="#00ffff"
                strokeWidth="2"
                opacity="0.7"
                className="hover:opacity-1 transition-opacity"
              />
              <text x="500" y="245" textAnchor="middle" fill="#00ffff" fontSize="12" fontWeight="bold">
                Training
              </text>
              <text x="500" y="260" textAnchor="middle" fill="#00ffff" fontSize="12" fontWeight="bold">
                Grid
              </text>
            </g>
            
            {/* Lumina Vault */}
            <g
              onClick={() => setSelectedZone('Lumina Vault')}
              className="cursor-pointer"
            >
              <circle
                cx="600"
                cy="300"
                r="50"
                fill={getZoneColor('Lumina Vault')}
                stroke="#00ffff"
                strokeWidth="2"
                opacity="0.7"
                className="hover:opacity-1 transition-opacity"
              />
              <text x="600" y="295" textAnchor="middle" fill="#00ffff" fontSize="12" fontWeight="bold">
                Lumina
              </text>
              <text x="600" y="310" textAnchor="middle" fill="#00ffff" fontSize="12" fontWeight="bold">
                Vault
              </text>
            </g>
            
            {/* Tranquility Core */}
            <g
              onClick={() => setSelectedZone('Tranquility Core')}
              className="cursor-pointer"
            >
              <ellipse
                cx="100"
                cy="250"
                rx="60"
                ry="40"
                fill={getZoneColor('Tranquility Core')}
                stroke="#00ffff"
                strokeWidth="2"
                opacity="0.7"
                className="hover:opacity-1 transition-opacity"
              />
              <text x="100" y="245" textAnchor="middle" fill="#00ffff" fontSize="12" fontWeight="bold">
                Tranquility
              </text>
              <text x="100" y="260" textAnchor="middle" fill="#00ffff" fontSize="12" fontWeight="bold">
                Core
              </text>
            </g>
            
            {/* Character Avatar */}
            <circle
              cx={50 + (totalProgress * 7)}
              cy={350 - (totalProgress * 3)}
              r="8"
              fill="#00ff9d"
              stroke="#00ffff"
              strokeWidth="2"
              className="animate-pulse"
            />
          </svg>
        </div>
      </div>

      {/* Zone Details Modal */}
      {selectedZone && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50" onClick={() => setSelectedZone(null)}>
          <div className="bg-card rounded-lg p-6 border border-neon-green max-w-md w-full mx-4" onClick={e => e.stopPropagation()}>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold text-neon-green">{selectedZone}</h3>
              <button
                onClick={() => setSelectedZone(null)}
                className="text-neon-cyan hover:text-neon-green"
              >
                <i className="fas fa-times"></i>
              </button>
            </div>
            
            <div className="space-y-3">
              {getZoneGoals(selectedZone).map(goal => (
                <div key={goal.id} className="bg-[#0a0a20] rounded p-3 border border-neon-green/30">
                  <div className="flex items-center justify-between">
                    <span className="text-neon-cyan">{goal.name}</span>
                    <span className="text-neon-green text-sm">
                      {goal.currentValue}/{goal.targetValue}
                    </span>
                  </div>
                  <div className="w-full bg-gray-800 rounded-full h-2 mt-2">
                    <div
                      className="bg-neon-green h-2 rounded-full transition-all duration-300"
                      style={{ width: `${Math.min((goal.currentValue / goal.targetValue) * 100, 100)}%` }}
                    ></div>
                  </div>
                </div>
              ))}
              
              {getZoneGoals(selectedZone).length === 0 && (
                <p className="text-neon-cyan/70 text-center py-4">
                  No quests in this zone yet. Create some goals to see them here!
                </p>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DashboardPage;
