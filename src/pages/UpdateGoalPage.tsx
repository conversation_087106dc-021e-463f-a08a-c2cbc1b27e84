import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { getGoals, updateGoalProgress, updateDebt, getAchievements } from '../services/api';
import { Goal, Achievement } from '../types';
import { ROUTES } from '../constants';
import CyberButton from '../components/CyberButton';
import CyberInput from '../components/CyberInput';
import HpBar from '../components/HpBar';
import LoadingSpinner from '../components/LoadingSpinner';

const UpdateGoalPage: React.FC = () => {
  const { goalId } = useParams<{ goalId: string }>();
  const navigate = useNavigate();
  
  const [goal, setGoal] = useState<Goal | null>(null);
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [error, setError] = useState<string>('');
  
  // Form state
  const [newValue, setNewValue] = useState<number>(0);
  const [newDebtAmount, setNewDebtAmount] = useState<number>(0);

  useEffect(() => {
    if (goalId) {
      loadGoalData();
    }
  }, [goalId]);

  const loadGoalData = async () => {
    if (!goalId) return;
    
    setLoading(true);
    try {
      const [goalsData, achievementsData] = await Promise.all([
        getGoals(),
        getAchievements(goalId),
      ]);
      
      const foundGoal = goalsData.find(g => g.id === goalId);
      if (!foundGoal) {
        setError('Goal not found');
        return;
      }
      
      setGoal(foundGoal);
      setNewValue(foundGoal.currentValue);
      setNewDebtAmount(foundGoal.debtAmount || 0);
      setAchievements(achievementsData);
    } catch (error) {
      console.error('Error loading goal data:', error);
      setError('Failed to load goal data');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateProgress = async () => {
    if (!goal || !goalId) return;
    
    setUpdating(true);
    setError('');
    
    try {
      const updatedGoal = await updateGoalProgress(goalId, newValue);
      setGoal(updatedGoal);
    } catch (error) {
      console.error('Error updating progress:', error);
      setError('Failed to update progress');
    } finally {
      setUpdating(false);
    }
  };

  const handleUpdateDebt = async () => {
    if (!goal || !goalId || !goal.isDebt) return;
    
    setUpdating(true);
    setError('');
    
    try {
      const updatedGoal = await updateDebt(goalId, newDebtAmount);
      setGoal(updatedGoal);
    } catch (error) {
      console.error('Error updating debt:', error);
      setError('Failed to update debt');
    } finally {
      setUpdating(false);
    }
  };

  const calculateProgress = () => {
    if (!goal) return 0;
    return Math.min((goal.currentValue / goal.targetValue) * 100, 100);
  };

  const getStreakIcon = (streak: number) => {
    if (streak >= 30) return 'fa-fire text-red-500';
    if (streak >= 7) return 'fa-fire text-orange-500';
    if (streak >= 3) return 'fa-fire text-yellow-500';
    return 'fa-fire text-gray-500';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <LoadingSpinner size="lg" text="Loading quest data..." />
      </div>
    );
  }

  if (error || !goal) {
    return (
      <div className="max-w-2xl mx-auto text-center space-y-4">
        <div className="bg-red-500/20 border border-red-500 rounded-lg p-6">
          <i className="fas fa-exclamation-triangle text-red-400 text-3xl mb-3"></i>
          <h2 className="text-xl font-bold text-red-400 mb-2">Error</h2>
          <p className="text-red-300">{error || 'Goal not found'}</p>
        </div>
        <CyberButton onClick={() => navigate(ROUTES.DASHBOARD)}>
          <i className="fas fa-arrow-left mr-2"></i>
          Back to Dashboard
        </CyberButton>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-4xl font-bold shadow-text">Update Quest</h1>
        <p className="text-neon-cyan">Track your progress and manage your journey</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Left Column - Goal Details */}
        <div className="space-y-6">
          {/* Goal Info */}
          <div className="bg-card rounded-lg p-6 border border-neon-green/30">
            <div className="flex items-start justify-between mb-4">
              <div>
                <h2 className="text-2xl font-bold text-neon-green">{goal.name}</h2>
                <p className="text-neon-cyan">{goal.category}</p>
              </div>
              <span className="px-3 py-1 bg-neon-green/20 border border-neon-green rounded-full text-neon-green text-sm">
                {goal.isDebt ? 'Debt Quest' : 'Progress Quest'}
              </span>
            </div>
            
            {goal.description && (
              <p className="text-neon-cyan/80 mb-4">{goal.description}</p>
            )}
            
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-neon-cyan">Created:</span>
                <p className="text-neon-green">
                  {new Date(goal.createdAt).toLocaleDateString()}
                </p>
              </div>
              <div>
                <span className="text-neon-cyan">Type:</span>
                <p className="text-neon-green">
                  {goal.dailyRepeatable ? 'Daily Repeatable' : 'One-time Goal'}
                </p>
              </div>
            </div>
          </div>

          {/* Progress Visualization */}
          <div className="bg-card rounded-lg p-6 border border-neon-green/30">
            <h3 className="text-xl font-bold text-neon-green mb-4">Progress Overview</h3>
            
            <HpBar
              current={goal.currentValue}
              max={goal.targetValue}
              label={`Progress ${goal.unit ? `(${goal.unit})` : ''}`}
              type="progress"
              className="mb-4"
            />
            
            <div className="grid grid-cols-2 gap-4 text-center">
              <div className="bg-[#0a0a20] rounded-lg p-3 border border-neon-green/30">
                <div className="text-2xl font-bold text-neon-green">
                  {Math.round(calculateProgress())}%
                </div>
                <div className="text-sm text-neon-cyan">Complete</div>
              </div>
              <div className="bg-[#0a0a20] rounded-lg p-3 border border-neon-green/30">
                <div className="text-2xl font-bold text-neon-green">
                  {goal.targetValue - goal.currentValue}
                </div>
                <div className="text-sm text-neon-cyan">Remaining</div>
              </div>
            </div>
          </div>

          {/* Streak Tracking */}
          <div className="bg-card rounded-lg p-6 border border-neon-green/30">
            <h3 className="text-xl font-bold text-neon-green mb-4">Streak Tracking</h3>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <div className="flex items-center justify-center space-x-2 mb-2">
                  <i className={`fas ${getStreakIcon(goal.currentStreak)} text-2xl`}></i>
                  <span className="text-2xl font-bold text-neon-green">
                    {goal.currentStreak}
                  </span>
                </div>
                <p className="text-sm text-neon-cyan">Current Streak</p>
              </div>
              
              <div className="text-center">
                <div className="flex items-center justify-center space-x-2 mb-2">
                  <i className="fas fa-crown text-yellow-500 text-2xl"></i>
                  <span className="text-2xl font-bold text-neon-green">
                    {goal.longestStreak}
                  </span>
                </div>
                <p className="text-sm text-neon-cyan">Best Streak</p>
              </div>
            </div>
          </div>
        </div>

        {/* Right Column - Update Forms */}
        <div className="space-y-6">
          {/* Progress Update */}
          <div className="bg-card rounded-lg p-6 border border-neon-green/30">
            <h3 className="text-xl font-bold text-neon-green mb-4">Update Progress</h3>
            
            <div className="space-y-4">
              <CyberInput
                type="number"
                label={`Current Value ${goal.unit ? `(${goal.unit})` : ''}`}
                value={newValue}
                onChange={(e) => setNewValue(Number(e.target.value))}
              />
              
              <div className="flex items-center justify-between text-sm text-neon-cyan">
                <span>Target: {goal.targetValue} {goal.unit || ''}</span>
                <span>
                  Progress: {Math.round((newValue / goal.targetValue) * 100)}%
                </span>
              </div>
              
              <CyberButton
                onClick={handleUpdateProgress}
                disabled={updating || newValue === goal.currentValue}
                className="w-full"
              >
                {updating ? (
                  <LoadingSpinner size="sm" />
                ) : (
                  <>
                    <i className="fas fa-arrow-up mr-2"></i>
                    Update Progress
                  </>
                )}
              </CyberButton>
            </div>
          </div>

          {/* Debt Update (if applicable) */}
          {goal.isDebt && (
            <div className="bg-card rounded-lg p-6 border border-red-500/30">
              <h3 className="text-xl font-bold text-red-400 mb-4">Update Debt</h3>
              
              <div className="space-y-4">
                <CyberInput
                  type="number"
                  label="Current Debt Amount"
                  value={newDebtAmount}
                  onChange={(e) => setNewDebtAmount(Number(e.target.value))}
                />
                
                <CyberButton
                  onClick={handleUpdateDebt}
                  disabled={updating || newDebtAmount === goal.debtAmount}
                  variant="danger"
                  className="w-full"
                >
                  {updating ? (
                    <LoadingSpinner size="sm" />
                  ) : (
                    <>
                      <i className="fas fa-edit mr-2"></i>
                      Update Debt
                    </>
                  )}
                </CyberButton>
              </div>
            </div>
          )}

          {/* Related Achievements */}
          <div className="bg-card rounded-lg p-6 border border-neon-cyan/30">
            <h3 className="text-xl font-bold text-neon-cyan mb-4">
              <i className="fas fa-trophy mr-2"></i>
              Related Achievements
            </h3>
            
            {achievements.length > 0 ? (
              <div className="space-y-3">
                {achievements.map(achievement => (
                  <div
                    key={achievement.id}
                    className={`p-3 rounded-lg border ${
                      achievement.unlocked
                        ? 'bg-neon-green/20 border-neon-green'
                        : 'bg-gray-800/50 border-gray-600'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <i className={`fas ${achievement.iconClass || 'fa-star'} ${
                          achievement.unlocked ? 'text-neon-green' : 'text-gray-500'
                        }`}></i>
                        <span className={achievement.unlocked ? 'text-neon-green' : 'text-gray-400'}>
                          {achievement.name}
                        </span>
                      </div>
                      {achievement.unlocked && (
                        <span className="text-xs text-neon-green">
                          <i className="fas fa-check mr-1"></i>
                          Unlocked
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-gray-400 mt-1">
                      {achievement.description}
                    </p>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-neon-cyan/70 text-center py-4">
                No achievements linked to this quest yet.
              </p>
            )}
          </div>

          {/* Error Display */}
          {error && (
            <div className="bg-red-500/20 border border-red-500 rounded-lg p-4">
              <p className="text-red-400">
                <i className="fas fa-exclamation-triangle mr-2"></i>
                {error}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Back Button */}
      <div className="text-center">
        <CyberButton
          onClick={() => navigate(ROUTES.DASHBOARD)}
          variant="secondary"
        >
          <i className="fas fa-arrow-left mr-2"></i>
          Back to Dashboard
        </CyberButton>
      </div>
    </div>
  );
};

export default UpdateGoalPage;
