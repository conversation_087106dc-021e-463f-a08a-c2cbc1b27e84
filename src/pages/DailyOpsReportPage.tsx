import React, { useState, useEffect } from 'react';
import { getDailyLogs, createDailyLog, getDailyLogByDate, getLumina, processTransactionsForLumina, processAIRequest } from '../services/api';
import { DailyLogEntry, BankTransaction } from '../types';
import { DEFAULTS } from '../constants';
import HpBar from '../components/HpBar';
import CyberButton from '../components/CyberButton';
import CyberInput from '../components/CyberInput';
import CyberTextArea from '../components/CyberTextArea';
import LoadingSpinner from '../components/LoadingSpinner';

const DailyOpsReportPage: React.FC = () => {
  const [currentDate, setCurrentDate] = useState(new Date().toISOString().split('T')[0]);
  const [dailyLog, setDailyLog] = useState<DailyLogEntry | null>(null);
  const [lumina, setLumina] = useState<number>(0);
  const [loading, setLoading] = useState(true);
  const [aiProcessing, setAiProcessing] = useState(false);
  
  // Form states
  const [rawInput, setRawInput] = useState('');
  const [hp, setHp] = useState<number>(DEFAULTS.MAX_HP);
  const [mp, setMp] = useState<number>(DEFAULTS.MAX_MP);
  const [ailments, setAilments] = useState<string>('');
  const [notes, setNotes] = useState('');
  
  // File upload
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [fileProcessing, setFileProcessing] = useState(false);

  useEffect(() => {
    loadDailyData();
  }, [currentDate]);

  const loadDailyData = async () => {
    setLoading(true);
    try {
      const [logData, luminaData] = await Promise.all([
        getDailyLogByDate(currentDate),
        getLumina(),
      ]);
      
      setDailyLog(logData);
      setLumina(luminaData);
      
      if (logData) {
        setHp(logData.currentHP);
        setMp(logData.currentMP);
        setAilments(logData.statusAilments.join(', '));
        setNotes(logData.notes || '');
        setRawInput(logData.rawUserInput || '');
      } else {
        // Reset to defaults for new day
        setHp(DEFAULTS.MAX_HP);
        setMp(DEFAULTS.MAX_MP);
        setAilments('');
        setNotes('');
        setRawInput('');
      }
    } catch (error) {
      console.error('Error loading daily data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDateChange = (direction: 'prev' | 'next') => {
    const date = new Date(currentDate);
    date.setDate(date.getDate() + (direction === 'next' ? 1 : -1));
    setCurrentDate(date.toISOString().split('T')[0]);
  };

  const handleAIProcess = async () => {
    if (!rawInput.trim()) return;
    
    setAiProcessing(true);
    try {
      const response = await processAIRequest(rawInput);
      
      if (response.type === 'daily_stats_summary' && response.payload) {
        const { hp: aiHp, mp: aiMp, status_ailments } = response.payload;
        if (aiHp !== undefined) setHp(aiHp);
        if (aiMp !== undefined) setMp(aiMp);
        if (status_ailments) setAilments(status_ailments.join(', '));
      }
    } catch (error) {
      console.error('Error processing AI request:', error);
    } finally {
      setAiProcessing(false);
    }
  };

  const handleSaveLog = async () => {
    try {
      const logData: Omit<DailyLogEntry, 'id' | 'timestamp'> = {
        date: currentDate,
        currentHP: hp,
        maxHP: DEFAULTS.MAX_HP,
        currentMP: mp,
        maxMP: DEFAULTS.MAX_MP,
        statusAilments: ailments.split(',').map(a => a.trim()).filter(a => a),
        rawUserInput: rawInput,
        notes,
      };
      
      const savedLog = await createDailyLog(logData);
      setDailyLog(savedLog);
    } catch (error) {
      console.error('Error saving daily log:', error);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setUploadedFile(file);
    }
  };

  const processCSVFile = async () => {
    if (!uploadedFile) return;
    
    setFileProcessing(true);
    try {
      const text = await uploadedFile.text();
      const lines = text.split('\n').slice(1); // Skip header
      const transactions: BankTransaction[] = [];
      
      lines.forEach(line => {
        const columns = line.split(',');
        if (columns.length >= 3) {
          const date = columns[0]?.trim();
          const description = columns[1]?.trim();
          const amount = parseFloat(columns[2]?.trim() || '0');
          
          if (date && description && !isNaN(amount)) {
            transactions.push({
              date,
              description,
              amount,
              type: amount > 0 ? 'CREDIT' : 'DEBIT',
              rawLine: line,
            });
          }
        }
      });
      
      const newBalance = await processTransactionsForLumina(transactions);
      setLumina(newBalance);
      setUploadedFile(null);
      
      // Reset file input
      const fileInput = document.getElementById('csv-upload') as HTMLInputElement;
      if (fileInput) fileInput.value = '';
      
    } catch (error) {
      console.error('Error processing CSV file:', error);
    } finally {
      setFileProcessing(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <LoadingSpinner size="lg" text="Loading daily operations..." />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-4xl font-bold shadow-text">Daily Ops Report</h1>
        <p className="text-neon-cyan">Monitor your daily stats and manage Lumina</p>
      </div>

      {/* Date Navigation */}
      <div className="flex items-center justify-center space-x-4">
        <CyberButton onClick={() => handleDateChange('prev')} size="sm">
          <i className="fas fa-chevron-left mr-2"></i>
          Previous Day
        </CyberButton>
        
        <div className="bg-card rounded-lg px-6 py-3 border border-neon-green/30">
          <h2 className="text-xl font-bold text-neon-green">
            {new Date(currentDate).toLocaleDateString('en-US', {
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric',
            })}
          </h2>
        </div>
        
        <CyberButton onClick={() => handleDateChange('next')} size="sm">
          Next Day
          <i className="fas fa-chevron-right ml-2"></i>
        </CyberButton>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Left Column - Stats Display */}
        <div className="space-y-6">
          {/* HP/MP Display */}
          <div className="bg-card rounded-lg p-6 border border-neon-green/30">
            <h3 className="text-xl font-bold text-neon-green mb-4">Current Stats</h3>
            <div className="space-y-4">
              <HpBar
                current={hp}
                max={DEFAULTS.MAX_HP}
                label="Health Points"
                type="hp"
              />
              <HpBar
                current={mp}
                max={DEFAULTS.MAX_MP}
                label="Mana Points"
                type="mp"
              />
            </div>
          </div>

          {/* Status Ailments */}
          <div className="bg-card rounded-lg p-6 border border-neon-green/30">
            <h3 className="text-xl font-bold text-neon-green mb-4">Status Effects</h3>
            {ailments ? (
              <div className="flex flex-wrap gap-2">
                {ailments.split(',').map((ailment, index) => (
                  <span
                    key={index}
                    className="px-3 py-1 bg-red-500/20 border border-red-500 rounded-full text-red-400 text-sm"
                  >
                    <i className="fas fa-exclamation-triangle mr-1"></i>
                    {ailment.trim()}
                  </span>
                ))}
              </div>
            ) : (
              <p className="text-neon-cyan/70">No status ailments</p>
            )}
          </div>

          {/* Lumina Balance */}
          <div className="bg-card rounded-lg p-6 border border-neon-green/30">
            <h3 className="text-xl font-bold text-neon-green mb-4">Lumina Balance</h3>
            <div className="flex items-center space-x-3">
              <i className="fas fa-sparkles text-neon-cyan text-2xl"></i>
              <span className="text-3xl font-bold text-neon-green">{lumina}</span>
            </div>
          </div>
        </div>

        {/* Right Column - Input Forms */}
        <div className="space-y-6">
          {/* AI-Assisted Input */}
          <div className="bg-card rounded-lg p-6 border border-neon-green/30">
            <h3 className="text-xl font-bold text-neon-green mb-4">AI Status Parser</h3>
            <div className="space-y-4">
              <CyberTextArea
                label="Describe your current state"
                placeholder="e.g., Woke up feeling drained, HP around 40. Got a headache. MP feels like 70."
                value={rawInput}
                onChange={(e) => setRawInput(e.target.value)}
                rows={3}
              />
              
              <CyberButton
                onClick={handleAIProcess}
                disabled={!rawInput.trim() || aiProcessing}
                className="w-full"
              >
                {aiProcessing ? (
                  <LoadingSpinner size="sm" />
                ) : (
                  <>
                    <i className="fas fa-magic mr-2"></i>
                    Process with AI
                  </>
                )}
              </CyberButton>
            </div>
          </div>

          {/* Manual Input */}
          <div className="bg-card rounded-lg p-6 border border-neon-green/30">
            <h3 className="text-xl font-bold text-neon-green mb-4">Manual Entry</h3>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <CyberInput
                  type="number"
                  label="HP"
                  value={hp}
                  onChange={(e) => setHp(Number(e.target.value))}
                />
                <CyberInput
                  type="number"
                  label="MP"
                  value={mp}
                  onChange={(e) => setMp(Number(e.target.value))}
                />
              </div>
              
              <CyberInput
                label="Status Ailments (comma-separated)"
                placeholder="Fatigue, Stress, Headache"
                value={ailments}
                onChange={(e) => setAilments(e.target.value)}
              />
              
              <CyberTextArea
                label="Notes"
                placeholder="Additional notes about your day..."
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={3}
              />
              
              <CyberButton onClick={handleSaveLog} className="w-full">
                <i className="fas fa-save mr-2"></i>
                Log Today's Status
              </CyberButton>
            </div>
          </div>

          {/* Lumina File Import */}
          <div className="bg-card rounded-lg p-6 border border-neon-green/30">
            <h3 className="text-xl font-bold text-neon-green mb-4">Bank Statement Import</h3>
            <div className="space-y-4">
              <CyberInput
                type="file"
                accept=".csv"
                label="Upload CSV Bank Statement"
                onChange={handleFileUpload}
              />
              
              {uploadedFile && (
                <div className="flex items-center justify-between p-3 bg-[#0a0a20] rounded border border-neon-green/30">
                  <span className="text-neon-cyan text-sm">{uploadedFile.name}</span>
                  <CyberButton
                    onClick={processCSVFile}
                    disabled={fileProcessing}
                    size="sm"
                  >
                    {fileProcessing ? (
                      <LoadingSpinner size="sm" />
                    ) : (
                      'Process'
                    )}
                  </CyberButton>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DailyOpsReportPage;
