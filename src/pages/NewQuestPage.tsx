import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { createGoal } from '../services/api';
import { GOAL_CATEGORIES, ROUTES } from '../constants';
import CyberButton from '../components/CyberButton';
import CyberInput from '../components/CyberInput';
import CyberTextArea from '../components/CyberTextArea';
import LoadingSpinner from '../components/LoadingSpinner';

const NewQuestPage: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  
  // Form state
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    category: '',
    targetValue: 1,
    isDebt: false,
    debtAmount: 0,
    dailyRepeatable: false,
    unit: '',
  });

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: '',
      }));
    }
  };

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};
    
    if (!formData.name.trim()) {
      newErrors.name = 'Quest name is required';
    }
    
    if (!formData.category) {
      newErrors.category = 'Category is required';
    }
    
    if (formData.targetValue <= 0) {
      newErrors.targetValue = 'Target value must be greater than 0';
    }
    
    if (formData.isDebt && formData.debtAmount <= 0) {
      newErrors.debtAmount = 'Debt amount must be greater than 0';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setLoading(true);
    try {
      await createGoal({
        name: formData.name,
        description: formData.description,
        category: formData.category,
        currentValue: 0,
        targetValue: formData.targetValue,
        isDebt: formData.isDebt,
        debtAmount: formData.isDebt ? formData.debtAmount : undefined,
        dailyRepeatable: formData.dailyRepeatable,
        unit: formData.unit || undefined,
      });
      
      navigate(ROUTES.DASHBOARD);
    } catch (error) {
      console.error('Error creating quest:', error);
      setErrors({ submit: 'Failed to create quest. Please try again.' });
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate(ROUTES.DASHBOARD);
  };

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-4xl font-bold shadow-text">Create New Quest</h1>
        <p className="text-neon-cyan">Define your next adventure</p>
      </div>

      {/* Form */}
      <div className="bg-card rounded-lg p-6 border border-neon-green/30">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Quest Name */}
          <CyberInput
            label="Quest Name"
            placeholder="e.g., Daily Workout, Learn TypeScript"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            error={errors.name}
            required
          />

          {/* Description */}
          <CyberTextArea
            label="Description"
            placeholder="Describe your quest objectives and motivation..."
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            rows={3}
          />

          {/* Category */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-neon-cyan">
              Category <span className="text-red-400">*</span>
            </label>
            <select
              value={formData.category}
              onChange={(e) => handleInputChange('category', e.target.value)}
              className="w-full px-4 py-3 bg-transparent border-2 border-neon-green/50 rounded-lg text-neon-green focus:border-neon-green focus:outline-none focus:shadow-cyberpunk-inset transition-all duration-300"
              required
            >
              <option value="" className="bg-[#1a1a35] text-neon-green">Select a category</option>
              {GOAL_CATEGORIES.map(category => (
                <option key={category} value={category} className="bg-[#1a1a35] text-neon-green">
                  {category}
                </option>
              ))}
            </select>
            {errors.category && (
              <p className="text-red-400 text-sm">
                <i className="fas fa-exclamation-triangle mr-1"></i>
                {errors.category}
              </p>
            )}
          </div>

          {/* Target Value and Unit */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <CyberInput
              type="number"
              label="Target Value"
              placeholder="100"
              value={formData.targetValue}
              onChange={(e) => handleInputChange('targetValue', Number(e.target.value))}
              error={errors.targetValue}
              required
            />
            
            <CyberInput
              label="Unit (optional)"
              placeholder="e.g., reps, miles, sessions"
              value={formData.unit}
              onChange={(e) => handleInputChange('unit', e.target.value)}
            />
          </div>

          {/* Daily Repeatable */}
          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              id="dailyRepeatable"
              checked={formData.dailyRepeatable}
              onChange={(e) => handleInputChange('dailyRepeatable', e.target.checked)}
              className="w-5 h-5 text-neon-green bg-transparent border-2 border-neon-green/50 rounded focus:ring-neon-green focus:ring-2"
            />
            <label htmlFor="dailyRepeatable" className="text-neon-cyan">
              Daily Repeatable Quest
            </label>
          </div>

          {/* Debt Options */}
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <input
                type="checkbox"
                id="isDebt"
                checked={formData.isDebt}
                onChange={(e) => handleInputChange('isDebt', e.target.checked)}
                className="w-5 h-5 text-neon-green bg-transparent border-2 border-neon-green/50 rounded focus:ring-neon-green focus:ring-2"
              />
              <label htmlFor="isDebt" className="text-neon-cyan">
                This is a debt/negative tracking quest
              </label>
            </div>
            
            {formData.isDebt && (
              <CyberInput
                type="number"
                label="Initial Debt Amount"
                placeholder="0"
                value={formData.debtAmount}
                onChange={(e) => handleInputChange('debtAmount', Number(e.target.value))}
                error={errors.debtAmount}
              />
            )}
          </div>

          {/* Submit Error */}
          {errors.submit && (
            <div className="p-4 bg-red-500/20 border border-red-500 rounded-lg">
              <p className="text-red-400">
                <i className="fas fa-exclamation-triangle mr-2"></i>
                {errors.submit}
              </p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex space-x-4">
            <CyberButton
              type="submit"
              disabled={loading}
              className="flex-1"
            >
              {loading ? (
                <LoadingSpinner size="sm" />
              ) : (
                <>
                  <i className="fas fa-plus mr-2"></i>
                  Create Quest
                </>
              )}
            </CyberButton>
            
            <CyberButton
              type="button"
              variant="secondary"
              onClick={handleCancel}
              disabled={loading}
            >
              <i className="fas fa-times mr-2"></i>
              Cancel
            </CyberButton>
          </div>
        </form>
      </div>

      {/* Quest Creation Tips */}
      <div className="bg-card rounded-lg p-6 border border-neon-cyan/30">
        <h3 className="text-lg font-bold text-neon-cyan mb-3">
          <i className="fas fa-lightbulb mr-2"></i>
          Quest Creation Tips
        </h3>
        <ul className="space-y-2 text-neon-cyan/80 text-sm">
          <li>• Make your quest specific and measurable</li>
          <li>• Choose realistic target values you can achieve</li>
          <li>• Use daily repeatable for habits you want to build</li>
          <li>• Debt quests are useful for tracking things you want to reduce</li>
          <li>• Add units to make progress more meaningful (e.g., "miles", "sessions")</li>
        </ul>
      </div>
    </div>
  );
};

export default NewQuestPage;
