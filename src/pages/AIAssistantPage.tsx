import React, { useState, useRef, useEffect } from 'react';
import { processAIRequest, createGoal, addAchievementFromAI, getGoals } from '../services/api';
import { AIResponse, Goal } from '../types';
import CyberButton from '../components/CyberButton';
import CyberTextArea from '../components/CyberTextArea';
import LoadingSpinner from '../components/LoadingSpinner';

interface ChatMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  aiResponse?: AIResponse;
  timestamp: number;
}

const AIAssistantPage: React.FC = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [input, setInput] = useState('');
  const [loading, setLoading] = useState(false);
  const [goals, setGoals] = useState<Goal[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    loadGoals();
    // Add welcome message
    setMessages([{
      id: '1',
      type: 'ai',
      content: 'Welcome to your AI Assistant! I can help you create quests, achievements, analyze your daily stats, and more. What would you like to do?',
      timestamp: Date.now(),
    }]);
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const loadGoals = async () => {
    try {
      const goalsData = await getGoals();
      setGoals(goalsData);
    } catch (error) {
      console.error('Error loading goals:', error);
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || loading) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: input,
      timestamp: Date.now(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setLoading(true);

    try {
      const aiResponse = await processAIRequest(input);
      
      const aiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: aiResponse.message || 'I processed your request.',
        aiResponse,
        timestamp: Date.now(),
      };

      setMessages(prev => [...prev, aiMessage]);
    } catch (error) {
      console.error('Error processing AI request:', error);
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: 'Sorry, I encountered an error processing your request. Please try again.',
        timestamp: Date.now(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setLoading(false);
    }
  };

  const handleAddQuest = async (questData: any) => {
    try {
      await createGoal({
        name: questData.name,
        description: questData.description,
        category: questData.category,
        currentValue: questData.currentValue || 0,
        targetValue: questData.targetValue,
        isDebt: questData.isDebt || false,
        debtAmount: questData.debtAmount,
        dailyRepeatable: questData.dailyRepeatable || false,
        unit: questData.unit,
      });
      
      await loadGoals();
      
      const confirmMessage: ChatMessage = {
        id: Date.now().toString(),
        type: 'ai',
        content: `✅ Quest "${questData.name}" has been added to your goals!`,
        timestamp: Date.now(),
      };
      setMessages(prev => [...prev, confirmMessage]);
    } catch (error) {
      console.error('Error adding quest:', error);
      const errorMessage: ChatMessage = {
        id: Date.now().toString(),
        type: 'ai',
        content: '❌ Failed to add quest. Please try again.',
        timestamp: Date.now(),
      };
      setMessages(prev => [...prev, errorMessage]);
    }
  };

  const handleAddAchievement = async (achievementData: any, linkedGoalId?: string) => {
    try {
      await addAchievementFromAI({
        name: achievementData.name,
        description: achievementData.description,
        condition: achievementData.condition,
        category: achievementData.category,
        linkedQuestName: linkedGoalId || null,
      });
      
      const confirmMessage: ChatMessage = {
        id: Date.now().toString(),
        type: 'ai',
        content: `🏆 Achievement "${achievementData.name}" has been added to your log!`,
        timestamp: Date.now(),
      };
      setMessages(prev => [...prev, confirmMessage]);
    } catch (error) {
      console.error('Error adding achievement:', error);
      const errorMessage: ChatMessage = {
        id: Date.now().toString(),
        type: 'ai',
        content: '❌ Failed to add achievement. Please try again.',
        timestamp: Date.now(),
      };
      setMessages(prev => [...prev, errorMessage]);
    }
  };

  const renderAIResponse = (message: ChatMessage) => {
    if (!message.aiResponse) return null;

    const { type, payload } = message.aiResponse;

    switch (type) {
      case 'quest_suggestion':
        return (
          <div className="mt-4 p-4 bg-[#0a0a20] rounded-lg border border-neon-green/30">
            <h4 className="text-neon-green font-bold mb-2">
              <i className="fas fa-plus mr-2"></i>
              Suggested Quest
            </h4>
            <div className="space-y-2 text-sm">
              <p><strong>Name:</strong> {payload.name}</p>
              <p><strong>Description:</strong> {payload.description}</p>
              <p><strong>Category:</strong> {payload.category}</p>
              <p><strong>Target:</strong> {payload.targetValue} {payload.unit || ''}</p>
              {payload.dailyRepeatable && <p><strong>Type:</strong> Daily Repeatable</p>}
              {payload.isDebt && <p><strong>Debt Amount:</strong> {payload.debtAmount}</p>}
            </div>
            <CyberButton
              onClick={() => handleAddQuest(payload)}
              size="sm"
              className="mt-3"
            >
              <i className="fas fa-plus mr-1"></i>
              Add This Quest
            </CyberButton>
          </div>
        );

      case 'achievement_template':
        return (
          <div className="mt-4 p-4 bg-[#0a0a20] rounded-lg border border-neon-cyan/30">
            <h4 className="text-neon-cyan font-bold mb-2">
              <i className="fas fa-trophy mr-2"></i>
              Suggested Achievement
            </h4>
            <div className="space-y-2 text-sm">
              <p><strong>Name:</strong> {payload.name}</p>
              <p><strong>Description:</strong> {payload.description}</p>
              <p><strong>Condition:</strong> {payload.condition}</p>
              <p><strong>Category:</strong> {payload.category}</p>
            </div>
            
            {payload.linkedQuestName && (
              <div className="mt-3">
                <label className="block text-xs text-neon-cyan mb-1">Link to Quest:</label>
                <select className="w-full px-2 py-1 bg-transparent border border-neon-green/50 rounded text-neon-green text-sm">
                  <option value="">Select a quest</option>
                  {goals.map(goal => (
                    <option key={goal.id} value={goal.id} className="bg-[#1a1a35]">
                      {goal.name}
                    </option>
                  ))}
                </select>
              </div>
            )}
            
            <CyberButton
              onClick={() => handleAddAchievement(payload)}
              size="sm"
              className="mt-3"
            >
              <i className="fas fa-trophy mr-1"></i>
              Add This Achievement
            </CyberButton>
          </div>
        );

      case 'daily_stats_summary':
      case 'daily_stats_trend':
      case 'daily_stats_comparison':
        return (
          <div className="mt-4 p-4 bg-[#0a0a20] rounded-lg border border-blue-500/30">
            <h4 className="text-blue-400 font-bold mb-2">
              <i className="fas fa-chart-line mr-2"></i>
              Stats Analysis
            </h4>
            <div className="space-y-1 text-sm">
              {Object.entries(payload).map(([key, value]) => (
                <p key={key}>
                  <strong className="capitalize">{key.replace(/_/g, ' ')}:</strong> {
                    Array.isArray(value) ? value.join(', ') : String(value)
                  }
                </p>
              ))}
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-4xl font-bold shadow-text">AI Assistant</h1>
        <p className="text-neon-cyan">Your intelligent companion for life optimization</p>
      </div>

      {/* Chat Interface */}
      <div className="bg-card rounded-lg border border-neon-green/30 h-[600px] flex flex-col">
        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-[80%] p-3 rounded-lg ${
                  message.type === 'user'
                    ? 'bg-neon-green/20 border border-neon-green text-neon-green'
                    : 'bg-neon-cyan/20 border border-neon-cyan text-neon-cyan'
                }`}
              >
                <div className="flex items-start space-x-2">
                  <i className={`fas ${message.type === 'user' ? 'fa-user' : 'fa-robot'} mt-1`}></i>
                  <div className="flex-1">
                    <p className="text-sm">{message.content}</p>
                    {message.type === 'ai' && renderAIResponse(message)}
                  </div>
                </div>
                <p className="text-xs opacity-70 mt-2">
                  {new Date(message.timestamp).toLocaleTimeString()}
                </p>
              </div>
            </div>
          ))}
          
          {loading && (
            <div className="flex justify-start">
              <div className="bg-neon-cyan/20 border border-neon-cyan text-neon-cyan p-3 rounded-lg">
                <div className="flex items-center space-x-2">
                  <i className="fas fa-robot"></i>
                  <LoadingSpinner size="sm" text="Thinking..." />
                </div>
              </div>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>

        {/* Input Form */}
        <div className="border-t border-neon-green/30 p-4">
          <form onSubmit={handleSubmit} className="flex space-x-3">
            <div className="flex-1">
              <CyberTextArea
                placeholder="Ask me to create quests, achievements, analyze your stats, or anything else..."
                value={input}
                onChange={(e) => setInput(e.target.value)}
                rows={2}
              />
            </div>
            <CyberButton
              type="submit"
              disabled={!input.trim() || loading}
              className="self-end"
            >
              <i className="fas fa-paper-plane"></i>
            </CyberButton>
          </form>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-card rounded-lg p-4 border border-neon-green/30">
          <h3 className="text-neon-green font-bold mb-2">
            <i className="fas fa-plus mr-2"></i>
            Quest Creation
          </h3>
          <p className="text-sm text-neon-cyan/80 mb-3">
            Ask me to suggest new quests based on your goals
          </p>
          <CyberButton
            onClick={() => setInput('Suggest a new fitness quest for me')}
            size="sm"
            className="w-full"
          >
            Try Example
          </CyberButton>
        </div>

        <div className="bg-card rounded-lg p-4 border border-neon-cyan/30">
          <h3 className="text-neon-cyan font-bold mb-2">
            <i className="fas fa-trophy mr-2"></i>
            Achievements
          </h3>
          <p className="text-sm text-neon-cyan/80 mb-3">
            Create achievements to celebrate your progress
          </p>
          <CyberButton
            onClick={() => setInput('Create an achievement for completing 10 workouts')}
            size="sm"
            variant="secondary"
            className="w-full"
          >
            Try Example
          </CyberButton>
        </div>

        <div className="bg-card rounded-lg p-4 border border-blue-500/30">
          <h3 className="text-blue-400 font-bold mb-2">
            <i className="fas fa-chart-line mr-2"></i>
            Stats Analysis
          </h3>
          <p className="text-sm text-neon-cyan/80 mb-3">
            Analyze your daily HP, MP, and status patterns
          </p>
          <CyberButton
            onClick={() => setInput('Show me my HP trends for this week')}
            size="sm"
            variant="secondary"
            className="w-full border-blue-500 text-blue-400 hover:bg-blue-500"
          >
            Try Example
          </CyberButton>
        </div>
      </div>
    </div>
  );
};

export default AIAssistantPage;
