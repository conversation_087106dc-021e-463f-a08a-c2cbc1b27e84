import React from 'react';

const DocumentationPage: React.FC = () => {
  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-4xl font-bold shadow-text">Documentation</h1>
        <p className="text-neon-cyan">Your guide to mastering the RPG Goals system</p>
      </div>

      {/* Quick Start Guide */}
      <div className="bg-card rounded-lg p-6 border border-neon-green/30">
        <h2 className="text-2xl font-bold text-neon-green mb-4">
          <i className="fas fa-rocket mr-2"></i>
          Quick Start Guide
        </h2>
        
        <div className="space-y-4">
          <div className="flex items-start space-x-3">
            <span className="flex-shrink-0 w-8 h-8 bg-neon-green text-[#0a0a20] rounded-full flex items-center justify-center font-bold">1</span>
            <div>
              <h3 className="font-bold text-neon-cyan">Create Your First Quest</h3>
              <p className="text-neon-cyan/80">Navigate to "New Quest" and define your first goal. Choose a category, set a target value, and decide if it's a daily repeatable quest.</p>
            </div>
          </div>
          
          <div className="flex items-start space-x-3">
            <span className="flex-shrink-0 w-8 h-8 bg-neon-green text-[#0a0a20] rounded-full flex items-center justify-center font-bold">2</span>
            <div>
              <h3 className="font-bold text-neon-cyan">Log Your Daily Stats</h3>
              <p className="text-neon-cyan/80">Visit "Daily Ops Report" to track your HP, MP, and status ailments. Use the AI assistant to parse natural language input.</p>
            </div>
          </div>
          
          <div className="flex items-start space-x-3">
            <span className="flex-shrink-0 w-8 h-8 bg-neon-green text-[#0a0a20] rounded-full flex items-center justify-center font-bold">3</span>
            <div>
              <h3 className="font-bold text-neon-cyan">Track Progress</h3>
              <p className="text-neon-cyan/80">Update your quest progress regularly and watch your life map evolve as you complete goals and unlock achievements.</p>
            </div>
          </div>
          
          <div className="flex items-start space-x-3">
            <span className="flex-shrink-0 w-8 h-8 bg-neon-green text-[#0a0a20] rounded-full flex items-center justify-center font-bold">4</span>
            <div>
              <h3 className="font-bold text-neon-cyan">Use AI Assistant</h3>
              <p className="text-neon-cyan/80">Chat with the AI to create new quests, achievements, and analyze your progress patterns.</p>
            </div>
          </div>
        </div>
      </div>

      {/* Core Concepts */}
      <div className="bg-card rounded-lg p-6 border border-neon-cyan/30">
        <h2 className="text-2xl font-bold text-neon-cyan mb-4">
          <i className="fas fa-brain mr-2"></i>
          Core Concepts
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-lg font-bold text-neon-green mb-2">Quests (Goals)</h3>
            <p className="text-neon-cyan/80 text-sm mb-3">
              Your personal objectives with measurable targets. Can be one-time goals or daily repeatable habits.
            </p>
            <ul className="text-sm text-neon-cyan/70 space-y-1">
              <li>• Set specific, measurable targets</li>
              <li>• Choose appropriate categories</li>
              <li>• Track progress over time</li>
              <li>• Build streaks for consistency</li>
            </ul>
          </div>
          
          <div>
            <h3 className="text-lg font-bold text-neon-green mb-2">Daily Stats</h3>
            <p className="text-neon-cyan/80 text-sm mb-3">
              Your daily health and energy levels represented as HP (Health Points) and MP (Mana Points).
            </p>
            <ul className="text-sm text-neon-cyan/70 space-y-1">
              <li>• HP: Physical health and energy</li>
              <li>• MP: Mental energy and focus</li>
              <li>• Status ailments: Temporary conditions</li>
              <li>• Track patterns over time</li>
            </ul>
          </div>
          
          <div>
            <h3 className="text-lg font-bold text-neon-green mb-2">Lumina Currency</h3>
            <p className="text-neon-cyan/80 text-sm mb-3">
              Your financial energy represented as a gamified currency that reflects your real financial transactions.
            </p>
            <ul className="text-sm text-neon-cyan/70 space-y-1">
              <li>• Import bank statements (CSV)</li>
              <li>• Automatic balance calculation</li>
              <li>• Visual representation on life map</li>
              <li>• Track financial progress</li>
            </ul>
          </div>
          
          <div>
            <h3 className="text-lg font-bold text-neon-green mb-2">Achievements</h3>
            <p className="text-neon-cyan/80 text-sm mb-3">
              Milestones and rewards for reaching specific goals or maintaining consistent habits.
            </p>
            <ul className="text-sm text-neon-cyan/70 space-y-1">
              <li>• Linked to specific quests</li>
              <li>• Unlock conditions</li>
              <li>• Progress celebration</li>
              <li>• AI-generated suggestions</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Life Map Guide */}
      <div className="bg-card rounded-lg p-6 border border-neon-green/30">
        <h2 className="text-2xl font-bold text-neon-green mb-4">
          <i className="fas fa-map mr-2"></i>
          Life Map Guide
        </h2>
        
        <p className="text-neon-cyan/80 mb-4">
          The Life Map is your visual progress dashboard, showing your journey through different life domains.
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <div className="flex items-center space-x-3">
              <div className="w-4 h-4 bg-blue-500 rounded"></div>
              <div>
                <h4 className="font-bold text-neon-cyan">Knowledge Spire</h4>
                <p className="text-sm text-neon-cyan/70">Learning & Productivity goals</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <div className="w-4 h-4 bg-green-500 rounded"></div>
              <div>
                <h4 className="font-bold text-neon-cyan">Training Grid</h4>
                <p className="text-sm text-neon-cyan/70">Fitness & Health goals</p>
              </div>
            </div>
          </div>
          
          <div className="space-y-3">
            <div className="flex items-center space-x-3">
              <div className="w-4 h-4 bg-yellow-500 rounded-full"></div>
              <div>
                <h4 className="font-bold text-neon-cyan">Lumina Vault</h4>
                <p className="text-sm text-neon-cyan/70">Financial goals & balance</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <div className="w-4 h-4 bg-purple-500 rounded-full"></div>
              <div>
                <h4 className="font-bold text-neon-cyan">Tranquility Core</h4>
                <p className="text-sm text-neon-cyan/70">Wellness, Mental & Social goals</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* AI Assistant Guide */}
      <div className="bg-card rounded-lg p-6 border border-neon-cyan/30">
        <h2 className="text-2xl font-bold text-neon-cyan mb-4">
          <i className="fas fa-robot mr-2"></i>
          AI Assistant Commands
        </h2>
        
        <div className="space-y-4">
          <div>
            <h3 className="font-bold text-neon-green mb-2">Quest Creation</h3>
            <div className="bg-[#0a0a20] rounded p-3 border border-neon-green/30">
              <p className="text-sm text-neon-cyan/80 mb-2">Example commands:</p>
              <ul className="text-sm text-neon-green space-y-1">
                <li>• "Create a daily workout quest for me"</li>
                <li>• "Suggest a learning goal for TypeScript"</li>
                <li>• "I want to track my reading habit"</li>
              </ul>
            </div>
          </div>
          
          <div>
            <h3 className="font-bold text-neon-green mb-2">Achievement Creation</h3>
            <div className="bg-[#0a0a20] rounded p-3 border border-neon-green/30">
              <p className="text-sm text-neon-cyan/80 mb-2">Example commands:</p>
              <ul className="text-sm text-neon-green space-y-1">
                <li>• "Create an achievement for 10 workout sessions"</li>
                <li>• "Make a milestone for reading 5 books"</li>
                <li>• "Achievement for maintaining 7-day streak"</li>
              </ul>
            </div>
          </div>
          
          <div>
            <h3 className="font-bold text-neon-green mb-2">Stats Analysis</h3>
            <div className="bg-[#0a0a20] rounded p-3 border border-neon-green/30">
              <p className="text-sm text-neon-cyan/80 mb-2">Example commands:</p>
              <ul className="text-sm text-neon-green space-y-1">
                <li>• "Show my HP trends this week"</li>
                <li>• "Analyze my energy patterns"</li>
                <li>• "Compare my MP today vs yesterday"</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Tips & Best Practices */}
      <div className="bg-card rounded-lg p-6 border border-yellow-500/30">
        <h2 className="text-2xl font-bold text-yellow-400 mb-4">
          <i className="fas fa-lightbulb mr-2"></i>
          Tips & Best Practices
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="font-bold text-neon-green mb-2">Goal Setting</h3>
            <ul className="text-sm text-neon-cyan/80 space-y-1">
              <li>• Start with small, achievable targets</li>
              <li>• Use specific, measurable units</li>
              <li>• Set both short-term and long-term goals</li>
              <li>• Review and adjust targets regularly</li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-bold text-neon-green mb-2">Daily Tracking</h3>
            <ul className="text-sm text-neon-cyan/80 space-y-1">
              <li>• Log stats consistently each day</li>
              <li>• Be honest about your energy levels</li>
              <li>• Note patterns and triggers</li>
              <li>• Use AI parsing for quick entry</li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-bold text-neon-green mb-2">Progress Tracking</h3>
            <ul className="text-sm text-neon-cyan/80 space-y-1">
              <li>• Update progress regularly</li>
              <li>• Celebrate small wins</li>
              <li>• Learn from setbacks</li>
              <li>• Focus on consistency over perfection</li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-bold text-neon-green mb-2">AI Usage</h3>
            <ul className="text-sm text-neon-cyan/80 space-y-1">
              <li>• Be specific in your requests</li>
              <li>• Use natural language descriptions</li>
              <li>• Ask for suggestions when stuck</li>
              <li>• Experiment with different commands</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DocumentationPage;
