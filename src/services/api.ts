import { Goal, Achievement, DailyLogEntry, DailyLogEntryInput, AIResponse, AchievementTemplate, BankTransaction } from '../types';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { v4 as uuidv4 } from 'uuid'; // For client-side ID generation

// --- localStorage Keys (for data not directly from PostgreSQL backend for simplicity) ---
const DAILY_LOGS_STORAGE_KEY = 'rpg_daily_logs';
const LUMINA_STORAGE_KEY = 'rpg_lumina_balance';
const ACHIEVEMENTS_STORAGE_KEY = 'rpg_ai_achievements'; // For AI-generated ones not yet saved to DB

// --- Gemini API Setup ---
// Ensure VITE_GEMINI_API_KEY is set in your environment variables (e.g., .env.local)
const GEMINI_API_KEY = import.meta.env.VITE_GEMINI_API_KEY;
if (!GEMINI_API_KEY) {
  console.error("GEMINI_API_KEY is not set. AI Assistant will not function.");
}
const genAI = GEMINI_API_KEY ? new GoogleGenerativeAI(GEMINI_API_KEY) : null;
const geminiModel = genAI ? genAI.getGenerativeModel({ model: "gemini-1.5-flash" }) : null; // Use gemini-1.5-flash or later

// --- Backend API Base URL (adjust if your backend is not on the same host) ---
// Assumes backend functions are exposed at /api/goals, /api/achievements etc.
const BACKEND_BASE_URL = '/api';

// --- Goals Management (Calls to backend) ---
export const getGoals = async (): Promise<Goal[]> => {
  try {
    const response = await fetch(`${BACKEND_BASE_URL}/goals`);
    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to fetch goals: ${response.status} ${response.statusText} - ${error}`);
    }
    const { goals } = await response.json();
    return goals;
  } catch (error) {
    console.error('Error in getGoals:', error);
    throw error; // Re-throw to be handled by calling component
  }
};

export const createGoal = async (newGoal: Omit<Goal, 'id' | 'currentStreak' | 'longestStreak' | 'createdAt' | 'currentValue'>): Promise<Goal> => {
  try {
    // Map frontend camelCase to backend snake_case for the request body
    const body = {
      name: newGoal.name,
      description: newGoal.description,
      category: newGoal.category,
      target_value: newGoal.targetValue,
      is_debt: newGoal.isDebt,
      debt_amount: newGoal.debtAmount,
      daily_repeatable: newGoal.dailyRepeatable,
      unit: newGoal.unit,
    };

    const response = await fetch(`${BACKEND_BASE_URL}/goals/create`, { // Assuming /api/goals/create endpoint
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to create goal: ${response.status} ${response.statusText} - ${error}`);
    }
    const { goal } = await response.json();
    return goal; // Backend should return the created goal in camelCase
  } catch (error) {
    console.error('Error in createGoal:', error);
    throw error;
  }
};

export const updateGoalProgress = async (goalId: string, currentValue: number): Promise<Goal> => {
  try {
    const body = { goalId, current_value: currentValue }; // Send snake_case to backend

    const response = await fetch(`${BACKEND_BASE_URL}/goals/update`, { // Assuming /api/goals/update endpoint
      method: 'PUT', // Or POST if REST convention is different
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to update goal progress: ${response.status} ${response.statusText} - ${error}`);
    }
    const { goal } = await response.json();
    return goal; // Backend should return the updated goal in camelCase
  } catch (error) {
    console.error('Error in updateGoalProgress:', error);
    throw error;
  }
};

export const updateDebt = async (goalId: string, debtAmount: number): Promise<Goal> => {
  try {
    const body = { goalId, debt_amount: debtAmount }; // Send snake_case

    const response = await fetch(`${BACKEND_BASE_URL}/goals/debt/update`, { // Assuming /api/goals/debt/update endpoint
      method: 'PUT', // Or POST
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to update debt: ${response.status} ${response.statusText} - ${error}`);
    }
    const { goal } = await response.json();
    return goal; // Backend should return the updated goal in camelCase
  } catch (error) {
    console.error('Error in updateDebt:', error);
    throw error;
  }
};

// --- Daily Logs Management (localStorage) ---
export const getDailyLogs = async (): Promise<DailyLogEntry[]> => {
  return new Promise(resolve => {
    const logsJson = localStorage.getItem(DAILY_LOGS_STORAGE_KEY);
    let logs: DailyLogEntry[] = logsJson ? JSON.parse(logsJson) : [];

    // Add mock data if empty
    if (logs.length === 0) {
      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      const twoDaysAgo = new Date(today);
      twoDaysAgo.setDate(twoDaysAgo.getDate() - 2);

      logs = [
        {
          id: uuidv4(),
          date: twoDaysAgo.toISOString().split('T')[0],
          currentHP: 80,
          maxHP: 100,
          currentMP: 60,
          maxMP: 100,
          statusAilments: [],
          rawUserInput: "Feeling okay today.",
          notes: "Had a good night's sleep.",
          timestamp: twoDaysAgo.getTime(),
        },
        {
          id: uuidv4(),
          date: yesterday.toISOString().split('T')[0],
          currentHP: 70,
          maxHP: 100,
          currentMP: 50,
          maxMP: 100,
          statusAilments: ["Fatigue"],
          rawUserInput: "Woke up feeling tired.",
          notes: "Didn't sleep well.",
          timestamp: yesterday.getTime(),
        },
        {
          id: uuidv4(),
          date: today.toISOString().split('T')[0],
          currentHP: 90,
          maxHP: 100,
          currentMP: 80,
          maxMP: 100,
          statusAilments: [],
          rawUserInput: "Feeling energetic!",
          notes: "Great day.",
          timestamp: today.getTime(),
        },
      ];
      localStorage.setItem(DAILY_LOGS_STORAGE_KEY, JSON.stringify(logs));
    }

    // Sort by date DESC
    logs.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

    resolve(logs);
  });
};

export const createDailyLog = async (newLog: Omit<DailyLogEntry, 'id' | 'timestamp'>): Promise<DailyLogEntry> => {
  return new Promise(resolve => {
    const logsJson = localStorage.getItem(DAILY_LOGS_STORAGE_KEY);
    const logs: DailyLogEntry[] = logsJson ? JSON.parse(logsJson) : [];

    const existingIndex = logs.findIndex(log => log.date === newLog.date);
    const timestamp = new Date().getTime();
    const logToSave: DailyLogEntry = {
      ...newLog,
      id: uuidv4(), // Generate new ID even if updating, simpler for this example
      timestamp: timestamp,
    };

    if (existingIndex > -1) {
      // Update existing entry
      logs[existingIndex] = logToSave;
    } else {
      // Add new entry
      logs.push(logToSave);
    }

    localStorage.setItem(DAILY_LOGS_STORAGE_KEY, JSON.stringify(logs));
    resolve(logToSave);
  });
};

export const updateDailyLog = async (updatedLog: DailyLogEntry): Promise<DailyLogEntry> => {
  return new Promise((resolve, reject) => {
    const logsJson = localStorage.getItem(DAILY_LOGS_STORAGE_KEY);
    const logs: DailyLogEntry[] = logsJson ? JSON.parse(logsJson) : [];

    const existingIndex = logs.findIndex(log => log.id === updatedLog.id);

    if (existingIndex > -1) {
      logs[existingIndex] = updatedLog;
      localStorage.setItem(DAILY_LOGS_STORAGE_KEY, JSON.stringify(logs));
      resolve(updatedLog);
    } else {
      reject(new Error('Daily log entry not found'));
    }
  });
};

export const getDailyLogByDate = async (date: string): Promise<DailyLogEntry | null> => {
  return new Promise(resolve => {
    const logsJson = localStorage.getItem(DAILY_LOGS_STORAGE_KEY);
    const logs: DailyLogEntry[] = logsJson ? JSON.parse(logsJson) : [];
    const log = logs.find(log => log.date === date);
    resolve(log || null);
  });
};

// --- Lumina Management (localStorage) ---
export const getLumina = async (): Promise<number> => {
  return new Promise(resolve => {
    const lumina = localStorage.getItem(LUMINA_STORAGE_KEY);
    resolve(lumina ? parseFloat(lumina) : 100); // Default to 100
  });
};

export const updateLumina = async (amount: number): Promise<number> => {
  return new Promise(resolve => {
    localStorage.setItem(LUMINA_STORAGE_KEY, amount.toString());
    resolve(amount);
  });
};

export const processTransactionsForLumina = async (transactions: BankTransaction[]): Promise<number> => {
  return new Promise(async (resolve) => {
    const currentLumina = await getLumina();
    const netChange = transactions.reduce((sum, transaction) => sum + transaction.amount, 0);
    const newLumina = currentLumina + netChange;
    await updateLumina(newLumina);
    resolve(newLumina);
  });
};

// --- Achievements Management (Mix of backend and localStorage for AI-generated) ---
export const getAchievements = async (goalId?: string): Promise<Achievement[]> => {
  try {
    // Fetch from backend endpoint
    const response = await fetch(`${BACKEND_BASE_URL}/achievements${goalId ? `?goalId=${goalId}` : ''}`);
    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to fetch achievements: ${response.status} ${response.statusText} - ${error}`);
    }
    const backendAchievements: Achievement[] = await response.json().then(data => data.achievements);

    // Get AI-generated achievements from localStorage
    const aiAchievementsJson = localStorage.getItem(ACHIEVEMENTS_STORAGE_KEY);
    const aiAchievements: Achievement[] = aiAchievementsJson ? JSON.parse(aiAchievementsJson) : [];

    // Combine and return unique achievements (backend takes precedence if IDs overlap, though unlikely)
    const combinedAchievements = [...backendAchievements];
    const backendIds = new Set(backendAchievements.map(a => a.id));

    for (const aiAch of aiAchievements) {
      if (!backendIds.has(aiAch.id)) {
        combinedAchievements.push(aiAch);
      }
    }

    // Sort by creation date (or requirement value if applicable, but spec says creation date for goals)
    // Let's sort by requirementValue for achievements as per backend spec
     combinedAchievements.sort((a, b) => (a.requirementValue ?? 0) - (b.requirementValue ?? 0));


    return combinedAchievements;

  } catch (error) {
    console.error('Error in getAchievements:', error);
    throw error;
  }
};

export const addAchievementFromAI = async (template: AchievementTemplate): Promise<Achievement> => {
  return new Promise(resolve => {
    const aiAchievementsJson = localStorage.getItem(ACHIEVEMENTS_STORAGE_KEY);
    const aiAchievements: Achievement[] = aiAchievementsJson ? JSON.parse(aiAchievementsJson) : [];

    // Convert template to full Achievement, generate client-side ID
    const newAchievement: Achievement = {
      id: uuidv4(),
      goalId: template.linkedQuestName || undefined, // Link by name for now, would need to resolve ID in a real app
      name: template.name,
      description: template.description,
      iconClass: 'fa-solid fa-trophy', // Default icon
      requirementValue: undefined, // AI template doesn't specify value, leave undefined
      unlocked: false,
      createdAt: new Date().getTime(),
    };

    aiAchievements.push(newAchievement);
    localStorage.setItem(ACHIEVEMENTS_STORAGE_KEY, JSON.stringify(aiAchievements));
    resolve(newAchievement);
  });
};


// --- AI Assistant Integration (Gemini API Call) ---
export const processAIRequest = async (userInput: string): Promise<AIResponse> => {
  if (!geminiModel) {
    return { type: 'error', message: 'AI Assistant is not configured. Please provide a GEMINI_API_KEY.' };
  }

  // IMPORTANT: Craft comprehensive prompt for Gemini
  const prompt = `You are an AI assistant for a "Level Up Your Life" RPG-themed goal tracking app.
Your goal is to help the user manage their life stats and create new gamified elements.
The user will provide a request. Analyze the request and respond in a structured JSON format.

Here's the data the app currently tracks (for context, do NOT invent data):
- Goals/Quests: id, name, description, category, currentValue, targetValue, streak, etc.
- Daily Logs: date, currentHP, maxHP, currentMP, maxMP, statusAilments, rawUserInput.
- Achievements: id, name, description, unlocked status, associated goalId.

Based on the user's input:

1.  **If the user asks to create a new achievement:**
    Respond with {"type": "achievement_template", "payload": {"name": "string", "description": "string", "condition": "string (natural language condition)", "category": "string (e.g., Health, Mental, Productivity, Quest-Specific)", "linkedQuestName": "string | null (Name of specific quest if inferred; otherwise null)"}}.
    Make the name and description compelling and RPG-themed.
    Example Achievement Condition: "Reach 100 HP", "Complete 'Study Code' quest 10 times", "Maintain MP above 80 for 5 consecutive days".

2.  **If the user asks for a new quest or goal suggestion (including daily workout quests):**
    Respond with {"type": "quest_suggestion", "payload": {
      "name": "string",
      "description": "string",
      "category": "string (e.g., Fitness, Learning, Financial, Creative, Social, Wellness)",
      "targetValue": "number (A sensible numerical target. Default to 1 if unitless like 'daily exercise' or 100 for skill XP.)",
      "currentValue": 0,
      "isDebt": "boolean (true if explicit debt/negative tracking, otherwise false)",
      "debtAmount": "number (optional, if isDebt is true)",
      "currentDebt": "number (optional, if isDebt is true, should be equal to debtAmount for new debt quests)",
      "dailyRepeatable": "boolean (true if explicitly for daily, repeatable tasks like workouts, otherwise false)",
      "unit": "string (Optional: 'miles', 'push-ups', 'sit-ups', 'reps', 'sets', 'minutes', 'sessions' for daily repeatable quests)"
    }}.
    Ensure the quest details are complete enough for a new Goal object.

3.  **If the user asks for a summary or query about their daily stats (HP, MP, ailments, etc.):**
    *   **For single-day summaries (today, specific date):**
        Respond with {"type": "daily_stats_summary", "payload": {"date": "string (YYYY-MM-DD)", "hp": "number (currentHP)", "mp": "number (currentMP)", "status_ailments": ["string"], "notes": "string"}, "message": "string (A concise summary)"}.
    *   **For multi-day summaries or trends (this week, last 3 days, etc.):**
        Respond with {"type": "daily_stats_trend", "payload": {"query": "string (original query)", "start_date": "string (YYYY-MM-DD)", "end_date": "string (YYYY-MM-DD)", "hp_average": "number", "mp_average": "number", "most_frequent_ailment": "string | 'None'", "days_with_ailments": "number"}, "message": "string (A concise trend summary)"}.
    *   **For comparisons (higher than yesterday, etc.):**
        Respond with {"type": "daily_stats_comparison", "payload": {"query": "string (original query)", "compared_stat": "string ('HP' or 'MP')", "current_value": "number", "compared_value": "number", "is_higher": "boolean", "difference": "number"}, "message": "string (A concise comparison)"}.
    (Note: The actual data retrieval for stats will happen on the frontend using existing API functions; the AI just helps identify the query and structure.)

4.  **For any other general question or command:**
    Respond with {"type": "general_response", "message": "string (a helpful, encouraging, or informational response)"}.

Always enclose the JSON in a markdown block (\`\`\`json...\`\`\`). Keep responses concise and directly to the point. If a numerical value is implied but not exact, use a reasonable default (e.g., 100 for a target value).

User Request: "${userInput}"
`;

    try {
      const result = await geminiModel.generateContent(prompt);
      const responseText = result.text(); // Assuming text() returns the markdown string

      // Extract JSON from markdown block
      const jsonMatch = responseText.match(/```json\n([\s\S]*?)\n```/);
      if (jsonMatch && jsonMatch[1]) {
        try {
          const aiResponse: AIResponse = JSON.parse(jsonMatch[1]);
          return aiResponse;
        } catch (jsonError) {
          console.error("Failed to parse AI response JSON:", jsonError);
          return { type: 'error', message: 'AI response was not valid JSON.' };
        }
      } else {
        // If no JSON block, return as general response
        return { type: 'general_response', message: responseText.trim() };
      }

    } catch (error) {
      console.error('Error calling Gemini API:', error);
      return { type: 'error', message: `Failed to get response from AI: ${error.message}` };
    }
};

// Basic CSV parsing function (assuming Date,Description,Amount or Date,Description,Debit,Credit)
export const parseCsvTransactions = (csvContent: string): BankTransaction[] => {
  const lines = csvContent.trim().split('\n');
  const transactions: BankTransaction[] = [];

  // Simple header detection (skip if first line looks like a header)
  const firstLine = lines[0].toLowerCase();
  const hasHeader = firstLine.includes('date') && firstLine.includes('amount');
  const dataLines = hasHeader ? lines.slice(1) : lines;

  for (const line of dataLines) {
    const columns = line.split(','); // Basic comma splitting
    if (columns.length < 3) continue; // Skip lines that don't look like transactions

    const date = columns[0].trim();
    const description = columns[1].trim();
    let amount = 0;
    let type = "UNKNOWN";

    // Try to parse amount. Handle single amount column or separate debit/credit
    if (columns.length >= 3) {
      const amountCol = parseFloat(columns[2].trim());
      if (!isNaN(amountCol)) {
        amount = amountCol;
        type = amount >= 0 ? "CREDIT" : "DEBIT";
      }
    }
    if (columns.length >= 4) { // Check for separate Debit/Credit columns
        const debit = parseFloat(columns[2].trim());
        const credit = parseFloat(columns[3].trim());
        if (!isNaN(credit) && credit > 0) {
            amount = credit;
            type = "CREDIT";
        } else if (!isNaN(debit) && debit > 0) {
            amount = -debit; // Debits are negative Lumina change
            type = "DEBIT";
        }
    }


    if (date && description && amount !== 0) { // Ensure essential fields are present
       // Basic date validation (YYYY-MM-DD)
       if (/^\d{4}-\d{2}-\d{2}$/.test(date)) {
         transactions.push({ date, description, amount, type, rawLine: line });
       } else {
         console.warn(`Skipping transaction with invalid date format: ${line}`);
       }
    } else {
        console.warn(`Skipping malformed transaction line: ${line}`);
    }
  }

  return transactions;
};
