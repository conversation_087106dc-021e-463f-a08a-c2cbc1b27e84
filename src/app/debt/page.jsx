"use client";
import React from "react";
import GameUi from "../../components/game-ui";
import BottomNav from "../../components/bottom-nav";

function MainComponent() {
  const [goals, setGoals] = useState([]);
  const [selectedGoal, setSelectedGoal] = useState(null);
  const [debtAmount, setDebtAmount] = useState("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);

  const fetchGoals = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/goals", { method: "POST" });
      if (!response.ok) {
        throw new Error("Failed to fetch debt goals");
      }
      const data = await response.json();
      const debtGoals = data.goals.filter((goal) => goal.is_debt);
      setGoals(debtGoals);
    } catch (err) {
      console.error(err);
      setError("Failed to load your debt goals");
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchGoals();
  }, [fetchGoals]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError(null);
    setSuccess(false);

    try {
      const response = await fetch("/api/goals/debt/update", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          goalId: selectedGoal,
          debtAmount: parseFloat(debtAmount),
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to update debt amount");
      }

      setSuccess(true);
      setDebtAmount("");
      setSelectedGoal(null);

      // Refresh goals after update
      await fetchGoals();

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(false);
      }, 3000);
    } catch (err) {
      console.error(err);
      setError("Failed to update debt amount");
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-[#0a0a20] text-[#00ff9d] pb-20">
        <GameUi health={100} maxHealth={100} currentLocation="Debt Tracker" />
        <div className="flex items-center justify-center h-screen">
          <div className="text-2xl">Loading your debt goals...</div>
        </div>
        <BottomNav currentPath="/debt" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#0a0a20] text-[#00ff9d] pb-20">
      <GameUi health={100} maxHealth={100} currentLocation="Debt Tracker" />
      <div className="p-6 pt-24">
        <div className="max-w-7xl mx-auto">
          <div className="flex justify-between items-center mb-8">
            <h1 className="text-4xl text-[#00ffff] font-bold">Debt Tracker</h1>
            <a
              href="/quests/new"
              className="px-6 py-3 bg-[#00ff9d] text-[#0a0a20] rounded-lg font-bold hover:bg-[#00cc7a] transition-colors"
            >
              Add New Debt
            </a>
          </div>

          <div className="flex flex-col lg:flex-row gap-6">
            <div className="w-full lg:w-2/3">
              <div className="bg-[#1a1a35] rounded-lg p-6 border-2 border-[#00ff9d] shadow-[0_0_15px_rgba(0,255,157,0.3)]">
                <h2 className="text-2xl mb-6 text-[#00ffff]">
                  Your Debt Goals
                </h2>
                <div className="space-y-4">
                  {goals.map((goal) => (
                    <div key={goal.id} className="bg-[#2a2a45] p-6 rounded-lg">
                      <div className="flex justify-between items-start mb-4">
                        <div>
                          <h3 className="text-xl font-bold mb-1">
                            {goal.name}
                          </h3>
                          <p className="text-sm text-[#00ffff]">
                            {goal.category}
                          </p>
                        </div>
                        <div className="text-right">
                          <div className="text-xl font-bold">
                            ${goal.debt_amount?.toLocaleString() || "0"}
                          </div>
                          <div className="text-sm text-[#00ffff]">
                            Current Balance
                          </div>
                        </div>
                      </div>
                      {goal.description && (
                        <p className="text-sm mb-4">{goal.description}</p>
                      )}
                    </div>
                  ))}

                  {goals.length === 0 && (
                    <div className="text-center py-8">
                      <p className="text-xl mb-4">No debt goals found</p>
                      <a
                        href="/quests/new"
                        className="inline-block px-6 py-3 bg-[#00ff9d] text-[#0a0a20] rounded-lg font-bold hover:bg-[#00cc7a] transition-colors"
                      >
                        Create a Debt Goal
                      </a>
                    </div>
                  )}
                </div>
              </div>
            </div>

            <div className="w-full lg:w-1/3">
              <div className="bg-[#1a1a35] rounded-lg p-6 border-2 border-[#00ff9d] shadow-[0_0_15px_rgba(0,255,157,0.3)]">
                <h2 className="text-2xl mb-6 text-[#00ffff]">
                  Update Debt Amount
                </h2>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                    <label className="block mb-2">Select Debt Goal</label>
                    <select
                      value={selectedGoal || ""}
                      onChange={(e) => setSelectedGoal(e.target.value)}
                      className="w-full p-2 rounded bg-[#2a2a45] border-2 border-[#00ff9d] text-[#00ff9d]"
                    >
                      <option value="">Choose a debt goal...</option>
                      {goals.map((goal) => (
                        <option key={goal.id} value={goal.id}>
                          {goal.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block mb-2">Current Balance</label>
                    <div className="relative">
                      <span className="absolute left-3 top-2 text-[#00ff9d]">
                        $
                      </span>
                      <input
                        type="number"
                        value={debtAmount}
                        onChange={(e) => setDebtAmount(e.target.value)}
                        className="w-full p-2 pl-8 rounded bg-[#2a2a45] border-2 border-[#00ff9d] text-[#00ff9d]"
                        placeholder="0.00"
                        step="0.01"
                      />
                    </div>
                  </div>

                  {error && (
                    <div className="p-4 bg-[#2a2a45] rounded-lg text-[#ff0000]">
                      {error}
                    </div>
                  )}

                  {success && (
                    <div className="p-4 bg-[#2a2a45] rounded-lg">
                      Debt amount updated successfully!
                    </div>
                  )}

                  <button
                    type="submit"
                    className="w-full py-3 px-4 bg-[#00ff9d] text-[#0a0a20] rounded-lg font-bold hover:bg-[#00cc7a] transition-colors"
                    disabled={!selectedGoal || !debtAmount}
                  >
                    Update Balance
                  </button>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
      <BottomNav currentPath="/debt" />
    </div>
  );
}

export default MainComponent;