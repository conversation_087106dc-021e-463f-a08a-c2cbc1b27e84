"use client";
import React from "react";

function MainComponent() {
  const [formData, setFormData] = useState({
    category: "",
    name: "",
    description: "",
    targetValue: "",
    isDebt: false,
    debtAmount: "",
  });
  const [achievements, setAchievements] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      const response = await fetch("/api/goals/create", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          ...formData,
          targetValue: parseFloat(formData.targetValue),
          debtAmount: formData.isDebt ? parseFloat(formData.debtAmount) : 0,
          achievements: achievements.map((a) => ({
            name: a.name,
            description: a.description,
            icon_class: "🏆",
            requirement_value: parseFloat(a.requirement_value),
          })),
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to create quest");
      }

      setSuccess(true);
      setFormData({
        category: "",
        name: "",
        description: "",
        targetValue: "",
        isDebt: false,
        debtAmount: "",
      });
      setAchievements([]);

      setTimeout(() => {
        window.location.href = "/quests";
      }, 2000);
    } catch (err) {
      console.error(err);
      setError("Failed to create your quest");
    } finally {
      setLoading(false);
    }
  };

  const addAchievement = () => {
    setAchievements([
      ...achievements,
      { name: "", description: "", requirement_value: "" },
    ]);
  };

  const updateAchievement = (index, field, value) => {
    const newAchievements = [...achievements];
    newAchievements[index] = { ...newAchievements[index], [field]: value };
    setAchievements(newAchievements);
  };

  const removeAchievement = (index) => {
    setAchievements(achievements.filter((_, i) => i !== index));
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-[#0a0a20] text-[#00ff9d] flex items-center justify-center">
        <div className="text-2xl">Creating your quest...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#0a0a20] text-[#00ff9d] pb-20">
      <div className="bg-[#1a1a35] p-4 fixed top-0 left-0 right-0 z-10">
        <div className="flex justify-between items-center">
          <div className="text-xl">New Quest</div>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <i className="fas fa-heart text-red-500"></i>
              <span>100/100</span>
            </div>
          </div>
        </div>
      </div>

      <div className="p-6 pt-24">
        <div className="max-w-2xl mx-auto">
          <div className="flex justify-between items-center mb-8">
            <h1 className="text-4xl text-[#00ffff] font-bold">
              Create New Quest
            </h1>
            <a
              href="/quests"
              className="px-6 py-3 bg-[#2a2a45] text-[#00ff9d] rounded-lg font-bold hover:bg-[#3a3a55] transition-colors"
            >
              Back to Quests
            </a>
          </div>

          <div className="bg-[#1a1a35] rounded-lg p-6 border-2 border-[#00ff9d] shadow-[0_0_15px_rgba(0,255,157,0.3)]">
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label className="block mb-2">Quest Category</label>
                <input
                  type="text"
                  value={formData.category}
                  onChange={(e) =>
                    setFormData({ ...formData, category: e.target.value })
                  }
                  className="w-full p-2 rounded bg-[#2a2a45] border-2 border-[#00ff9d] text-[#00ff9d]"
                  placeholder="e.g., Fitness, Learning, Meditation"
                  required
                />
              </div>

              <div>
                <label className="block mb-2">Quest Name</label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) =>
                    setFormData({ ...formData, name: e.target.value })
                  }
                  className="w-full p-2 rounded bg-[#2a2a45] border-2 border-[#00ff9d] text-[#00ff9d]"
                  placeholder="e.g., Daily Meditation"
                  required
                />
              </div>

              <div>
                <label className="block mb-2">Description</label>
                <textarea
                  value={formData.description}
                  onChange={(e) =>
                    setFormData({ ...formData, description: e.target.value })
                  }
                  className="w-full p-2 rounded bg-[#2a2a45] border-2 border-[#00ff9d] text-[#00ff9d]"
                  placeholder="Describe your quest..."
                  rows="3"
                />
              </div>

              <div className="flex items-center gap-2 mb-4">
                <input
                  type="checkbox"
                  id="isDebt"
                  checked={formData.isDebt}
                  onChange={(e) =>
                    setFormData({ ...formData, isDebt: e.target.checked })
                  }
                  className="w-4 h-4 bg-[#2a2a45] border-2 border-[#00ff9d]"
                />
                <label htmlFor="isDebt">This is a debt goal</label>
              </div>

              {formData.isDebt && (
                <div>
                  <label className="block mb-2">Initial Debt Amount</label>
                  <div className="relative">
                    <span className="absolute left-3 top-2 text-[#00ff9d]">
                      $
                    </span>
                    <input
                      type="number"
                      value={formData.debtAmount}
                      onChange={(e) =>
                        setFormData({ ...formData, debtAmount: e.target.value })
                      }
                      className="w-full p-2 pl-8 rounded bg-[#2a2a45] border-2 border-[#00ff9d] text-[#00ff9d]"
                      placeholder="0.00"
                      step="0.01"
                      required={formData.isDebt}
                    />
                  </div>
                </div>
              )}

              <div>
                <label className="block mb-2">Target Value</label>
                <input
                  type="number"
                  value={formData.targetValue}
                  onChange={(e) =>
                    setFormData({ ...formData, targetValue: e.target.value })
                  }
                  className="w-full p-2 rounded bg-[#2a2a45] border-2 border-[#00ff9d] text-[#00ff9d]"
                  placeholder={
                    formData.isDebt
                      ? "Target debt amount (0 for paid off)"
                      : "e.g., 30 (for 30 days)"
                  }
                  required
                />
              </div>

              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <label className="text-xl text-[#00ffff]">Achievements</label>
                  <button
                    type="button"
                    onClick={addAchievement}
                    className="px-4 py-2 bg-[#2a2a45] rounded-lg hover:bg-[#3a3a55] transition-colors"
                  >
                    Add Achievement
                  </button>
                </div>

                {achievements.map((achievement, index) => (
                  <div
                    key={index}
                    className="bg-[#2a2a45] p-4 rounded-lg space-y-3"
                  >
                    <div className="flex justify-between">
                      <span className="text-[#00ffff]">
                        Achievement #{index + 1}
                      </span>
                      <button
                        type="button"
                        onClick={() => removeAchievement(index)}
                        className="text-[#ff0000] hover:text-[#ff3333]"
                      >
                        Remove
                      </button>
                    </div>
                    <input
                      type="text"
                      value={achievement.name}
                      onChange={(e) =>
                        updateAchievement(index, "name", e.target.value)
                      }
                      className="w-full p-2 rounded bg-[#1a1a35] border-2 border-[#00ff9d] text-[#00ff9d]"
                      placeholder="Achievement name"
                      required
                    />
                    <input
                      type="text"
                      value={achievement.description}
                      onChange={(e) =>
                        updateAchievement(index, "description", e.target.value)
                      }
                      className="w-full p-2 rounded bg-[#1a1a35] border-2 border-[#00ff9d] text-[#00ff9d]"
                      placeholder="Achievement description"
                      required
                    />
                    <input
                      type="number"
                      value={achievement.requirement_value}
                      onChange={(e) =>
                        updateAchievement(
                          index,
                          "requirement_value",
                          e.target.value
                        )
                      }
                      className="w-full p-2 rounded bg-[#1a1a35] border-2 border-[#00ff9d] text-[#00ff9d]"
                      placeholder="Required value to unlock"
                      required
                    />
                  </div>
                ))}
              </div>

              {error && (
                <div className="p-4 bg-[#2a2a45] rounded-lg text-[#ff0000]">
                  {error}
                </div>
              )}

              {success && (
                <div className="fixed top-4 right-4 p-4 bg-[#1a1a35] rounded-lg border-2 border-[#00ff9d] shadow-[0_0_15px_rgba(0,255,157,0.3)]">
                  Quest created successfully! Redirecting...
                </div>
              )}

              <button
                type="submit"
                className="w-full py-3 px-4 bg-[#00ff9d] text-[#0a0a20] rounded-lg font-bold hover:bg-[#00cc7a] transition-colors"
                disabled={loading}
              >
                Create Quest
              </button>
            </form>
          </div>
        </div>
      </div>

      <></>
    </div>
  );
}

export default MainComponent;