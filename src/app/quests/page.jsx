"use client";
import React from "react";
import GameUi from "../../components/game-ui";

function MainComponent() {
  const [goals, setGoals] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchGoals = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/goals", { method: "POST" });
      if (!response.ok) {
        throw new Error("Failed to fetch quests");
      }
      const data = await response.json();
      setGoals(data.goals);
    } catch (err) {
      console.error(err);
      setError("Failed to load your quests");
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchGoals();
  }, [fetchGoals]);

  if (loading) {
    return (
      <div className="min-h-screen bg-[#0a0a20] text-[#00ff9d] pb-20">
        <GameUi health={100} maxHealth={100} currentLocation="Quests" />
        <div className="flex items-center justify-center h-screen">
          <div className="text-2xl">Loading your quests...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-[#0a0a20] text-[#ff0000]">
        <GameUi health={100} maxHealth={100} currentLocation="Quests" />
        <div className="flex items-center justify-center h-screen">
          <div className="text-2xl">{error}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#0a0a20] text-[#00ff9d] pb-20">
      <GameUi health={100} maxHealth={100} currentLocation="Quests" />

      <div className="p-6 pt-24">
        <div className="max-w-7xl mx-auto">
          <div className="flex justify-between items-center mb-8">
            <h1 className="text-4xl text-[#00ffff] font-bold">Your Quests</h1>
            <a
              href="/quests/new"
              className="px-6 py-3 bg-[#00ff9d] text-[#0a0a20] rounded-lg font-bold hover:bg-[#00cc7a] transition-colors"
            >
              New Quest
            </a>
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {goals.map((goal) => (
              <div
                key={goal.id}
                className="bg-[#1a1a35] rounded-lg p-6 border-2 border-[#00ff9d] shadow-[0_0_15px_rgba(0,255,157,0.3)]"
              >
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h2 className="text-xl font-bold mb-1">{goal.name}</h2>
                    <span className="text-sm text-[#00ffff]">
                      {goal.category}
                    </span>
                  </div>
                  <a
                    href="/update"
                    className="px-4 py-2 bg-[#2a2a45] rounded-lg hover:bg-[#3a3a55] transition-colors text-sm"
                  >
                    Update
                  </a>
                </div>

                {goal.description && (
                  <p className="text-sm mb-4">{goal.description}</p>
                )}

                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Progress</span>
                      <span>
                        {goal.current_value} / {goal.target_value}
                      </span>
                    </div>
                    <div className="h-2 bg-[#2a2a45] rounded-full overflow-hidden">
                      <div
                        className="h-full bg-[#00ff9d] transition-all duration-500"
                        style={{
                          width: `${Math.min(
                            (goal.current_value / goal.target_value) * 100,
                            100
                          )}%`,
                        }}
                      />
                    </div>
                  </div>

                  <div className="flex justify-between text-sm">
                    <div className="flex items-center gap-1">
                      <span>🔥</span>
                      <span>Current Streak: {goal.current_streak}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <span>⭐</span>
                      <span>Best Streak: {goal.longest_streak}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {goals.length === 0 && (
            <div className="text-center py-12">
              <p className="text-xl mb-4">
                You haven't created any quests yet!
              </p>
              <a
                href="/quests/new"
                className="inline-block px-6 py-3 bg-[#00ff9d] text-[#0a0a20] rounded-lg font-bold hover:bg-[#00cc7a] transition-colors"
              >
                Create Your First Quest
              </a>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default MainComponent;