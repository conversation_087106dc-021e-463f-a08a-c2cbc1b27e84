"use client";
import React from "react";
import GameUi from "../../components/game-ui";
import BottomNav from "../../components/bottom-nav";

function GameUi({ health, maxHealth, currentLocation }) {
  return (
    <div className="fixed top-0 left-0 right-0 bg-[#1a1a35] border-b-2 border-[#00ff9d] p-4 z-50">
      <div className="max-w-7xl mx-auto flex justify-between items-center">
        <div className="text-xl text-[#00ffff]">{currentLocation}</div>
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <span className="text-[#ff0066]">HP</span>
            <span className="text-[#ff0066]">
              {health}/{maxHealth}
            </span>
          </div>
          <div className="w-32 h-4 bg-[#2a2a45] rounded-full overflow-hidden">
            <div
              className="h-full bg-[#ff0066] transition-all duration-300"
              style={{ width: `${(health / maxHealth) * 100}%` }}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

function BottomNav({ currentPath }) {
  return (
    <div className="fixed bottom-0 left-0 right-0 bg-[#1a1a35] border-t-2 border-[#00ff9d] p-4">
      <div className="max-w-7xl mx-auto flex justify-around">
        <a
          href="/"
          className={`px-4 py-2 rounded-lg ${
            currentPath === "/"
              ? "bg-[#00ff9d] text-[#0a0a20]"
              : "text-[#00ff9d]"
          }`}
        >
          Quests
        </a>
        <a
          href="/debt"
          className={`px-4 py-2 rounded-lg ${
            currentPath === "/debt"
              ? "bg-[#00ff9d] text-[#0a0a20]"
              : "text-[#00ff9d]"
          }`}
        >
          Debt
        </a>
      </div>
    </div>
  );
}

function MainComponent() {
  const [goals, setGoals] = useState([]);
  const [selectedGoal, setSelectedGoal] = useState(null);
  const [progressValue, setProgressValue] = useState("");
  const [progressNotes, setProgressNotes] = useState("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const goalId = params.get("goalId");
    if (goalId) {
      setSelectedGoal(goalId);
    }
  }, []);

  const fetchGoals = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/goals", { method: "POST" });
      if (!response.ok) {
        throw new Error("Failed to fetch goals");
      }
      const data = await response.json();
      setGoals(data.goals);
    } catch (err) {
      console.error(err);
      setError("Failed to load goals");
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchGoals();
  }, [fetchGoals]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!selectedGoal || !progressValue) return;

    try {
      const response = await fetch("/api/goals/update", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          goalId: selectedGoal,
          value: parseFloat(progressValue),
          notes: progressNotes,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to update progress");
      }

      setSuccess(true);
      setProgressValue("");
      setProgressNotes("");

      await fetchGoals();

      setTimeout(() => {
        setSuccess(false);
      }, 3000);
    } catch (err) {
      console.error(err);
      setError("Failed to update progress");
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-[#0a0a20] text-[#00ff9d] pb-20">
        <div className="flex items-center justify-center h-screen">
          <div className="text-2xl">Loading your quests...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#0a0a20] text-[#00ff9d] pb-20">
      <GameUi health={100} maxHealth={100} currentLocation="Update Quest" />
      <div className="p-6 pt-24">
        <div className="max-w-7xl mx-auto">
          <div className="flex justify-between items-center mb-8">
            <h1 className="text-4xl text-[#00ffff] font-bold">
              Update Quest Progress
            </h1>
            <a
              href="/quests"
              className="px-6 py-3 bg-[#2a2a45] text-[#00ff9d] rounded-lg font-bold hover:bg-[#3a3a55] transition-colors"
            >
              Back to Quests
            </a>
          </div>

          <div className="flex flex-col lg:flex-row gap-6">
            <div className="w-full lg:w-1/2">
              <div className="bg-[#1a1a35] rounded-lg p-6 border-2 border-[#00ff9d] shadow-[0_0_15px_rgba(0,255,157,0.3)]">
                <h2 className="text-2xl mb-6 text-[#00ffff]">Manual Update</h2>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                    <label className="block mb-2">Select Quest</label>
                    <select
                      value={selectedGoal || ""}
                      onChange={(e) => setSelectedGoal(e.target.value)}
                      className="w-full p-2 rounded bg-[#2a2a45] border-2 border-[#00ff9d] text-[#00ff9d]"
                    >
                      <option value="">Choose a quest...</option>
                      {goals.map((goal) => (
                        <option key={goal.id} value={goal.id}>
                          {goal.name}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block mb-2">Progress Value</label>
                    <input
                      type="number"
                      value={progressValue}
                      onChange={(e) => setProgressValue(e.target.value)}
                      className="w-full p-2 rounded bg-[#2a2a45] border-2 border-[#00ff9d] text-[#00ff9d]"
                      placeholder="Enter progress value..."
                    />
                  </div>
                  <div>
                    <label className="block mb-2">Notes (optional)</label>
                    <textarea
                      value={progressNotes}
                      onChange={(e) => setProgressNotes(e.target.value)}
                      className="w-full p-2 rounded bg-[#2a2a45] border-2 border-[#00ff9d] text-[#00ff9d]"
                      placeholder="Add notes about your progress..."
                    />
                  </div>
                  <button
                    type="submit"
                    className="w-full py-2 px-4 bg-[#00ff9d] text-[#0a0a20] rounded-lg font-bold hover:bg-[#00cc7a] transition-colors"
                    disabled={!selectedGoal || !progressValue}
                  >
                    Update Progress
                  </button>
                </form>

                {error && (
                  <div className="mt-4 p-4 bg-[#2a2a45] rounded-lg text-[#ff0000]">
                    {error}
                  </div>
                )}

                {success && (
                  <div className="mt-4 p-4 bg-[#2a2a45] rounded-lg text-[#00ff9d]">
                    Progress updated successfully!
                  </div>
                )}
              </div>
            </div>

            <div className="w-full lg:w-1/2">
              <div className="bg-[#1a1a35] rounded-lg p-6 border-2 border-[#00ff9d] shadow-[0_0_15px_rgba(0,255,157,0.3)]">
                <h2 className="text-2xl mb-6 text-[#00ffff]">
                  API Documentation
                </h2>
                <div className="space-y-6">
                  <div>
                    <h3 className="text-xl mb-2 text-[#00ffff]">Endpoint</h3>
                    <div className="bg-[#2a2a45] p-4 rounded-lg">
                      <code>POST /api/goals/update</code>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-xl mb-2 text-[#00ffff]">
                      Request Body
                    </h3>
                    <div className="bg-[#2a2a45] p-4 rounded-lg">
                      <pre>{`{
  "goalId": "string",
  "value": number,
  "notes": "string" (optional)
}`}</pre>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-xl mb-2 text-[#00ffff]">Response</h3>
                    <div className="bg-[#2a2a45] p-4 rounded-lg">
                      <pre>{`{
  "goal": {
    "id": "string",
    "name": "string",
    "current_value": number,
    "target_value": number,
    "current_streak": number,
    "longest_streak": number
  },
  "unlockedAchievements": [
    {
      "id": "string",
      "name": "string",
      "description": "string"
    }
  ]
}`}</pre>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-xl mb-2 text-[#00ffff]">
                      Example Usage
                    </h3>
                    <div className="bg-[#2a2a45] p-4 rounded-lg">
                      <pre>{`fetch('/api/goals/update', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    goalId: "goal_id",
    value: 1,
    notes: "Completed daily meditation"
  })
})`}</pre>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <BottomNav currentPath="/update" />
    </div>
  );
}

export default MainComponent;