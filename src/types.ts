// UUID import is used in services/api.ts

export interface Goal {
  id: string;
  name: string;
  description?: string;
  category: string;
  currentValue: number;
  targetValue: number;
  isDebt: boolean;
  debtAmount?: number;
  currentStreak: number;
  longestStreak: number;
  createdAt: number; // Unix timestamp
  dailyRepeatable?: boolean; // NEW
  unit?: string; // NEW
}

export interface Achievement {
  id: string;
  goalId?: string; // Nullable
  name: string;
  description: string;
  iconClass?: string;
  requirementValue?: number;
  unlocked: boolean;
  unlockedAt?: number;
  createdAt: number; // Unix timestamp
}

export interface DailyLogEntry {
  id: string;
  date: string; // YYYY-MM-DD
  currentHP: number;
  maxHP: number;
  currentMP: number;
  maxMP: number;
  statusAilments: string[]; // List of ailments
  rawUserInput?: string;
  notes?: string;
  timestamp: number; // Unix timestamp
}

export interface DailyLogEntryInput {
  rawInput: string;
  hp?: number;
  mp?: number;
  statusAilments?: string[];
}

// For AI responses
export interface AIResponse {
  type: 'achievement_template' | 'daily_stats_query' | 'quest_suggestion' | 'general_response' | 'daily_stats_summary' | 'daily_stats_trend' | 'daily_stats_comparison' | 'error';
  payload?: any;
  message?: string;
}

// For AI-generated achievement proposals
export interface AchievementTemplate {
  name: string;
  description: string;
  condition?: string;
  category?: string;
  linkedQuestName?: string | null; // Name of quest to link to
}

// For parsing bank transactions
export interface BankTransaction {
  date: string; // YYYY-MM-DD
  description: string;
  amount: number; // Positive for credit, negative for debit
  type: string; // "DEBIT", "CREDIT"
  rawLine?: string; // Original line from file
}
