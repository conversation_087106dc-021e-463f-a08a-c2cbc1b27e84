import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  goals: defineTable({
    name: v.string(),
    description: v.optional(v.string()),
    category: v.optional(v.string()),
    current_value: v.optional(v.number()),
    target_value: v.optional(v.number()),
    is_debt: v.optional(v.boolean()),
    debt_amount: v.optional(v.number()),
    current_streak: v.optional(v.number()),
    longest_streak: v.optional(v.number()),
    daily_repeatable: v.optional(v.boolean()),
    unit: v.optional(v.string()),
  }),
  achievements: defineTable({
    goal_id: v.optional(v.id("goals")),
    name: v.string(),
    description: v.optional(v.string()),
    icon_class: v.optional(v.string()),
    requirement_value: v.optional(v.number()),
    unlocked: v.optional(v.boolean()),
  }),
});